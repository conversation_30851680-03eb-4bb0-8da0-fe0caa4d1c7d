const swaggerUi = require('swagger-ui-express');
let swaggerJson = require('../public/swagger.json');

// Auth
swaggerJson = require('../services/signin/signInSwagger')(swaggerJson);
swaggerJson = require('../services/forgotPassword/forgotPasswordSwagger')(
    swaggerJson
);

// User
swaggerJson = require('../services/userProfile/userProfileSwagger')(swaggerJson);
swaggerJson = require('../services/listUser/listUserSwagger')(swaggerJson);
swaggerJson = require('../services/listMember/listMemberSwagger')(swaggerJson);
swaggerJson = require('../services/listProjectManager/listProjectManagerSwagger')(swaggerJson);
swaggerJson = require('../services/mentee/menteeSwagger')(swaggerJson);
swaggerJson = require('../services/getAttendance/getAttendanceSwagger')(swaggerJson);
swaggerJson = require('../services/downloadAttendance/downloadAttendanceSwagger')(swaggerJson);
swaggerJson = require('../services/downloadBillingReport/downloadBillingReportSwagger')(swaggerJson);
swaggerJson = require('../services/addLeave/addLeaveSwagger')(swaggerJson);
swaggerJson = require('../services/uploadLeave/uploadLeaveSwagger')(swaggerJson);
swaggerJson = require('../services/changeStatusUser/changeStatusUserSwagger')(swaggerJson);
swaggerJson = require('../services/userProfile/userProfileSwagger')(
    swaggerJson
);
swaggerJson = require('../services/listUser/listUserSwagger')(swaggerJson);
swaggerJson = require('../services/listMember/listMemberSwagger')(swaggerJson);
swaggerJson =
  require('../services/listProjectManager/listProjectManagerSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/getAttendance/getAttendanceSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/downloadAttendance/downloadAttendanceSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadBillingReport/downloadBillingReportSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/addLeave/addLeaveSwagger')(swaggerJson);
swaggerJson = require('../services/uploadLeave/uploadLeaveSwagger')(
    swaggerJson
);
swaggerJson = require('../services/changeStatusUser/changeStatusUserSwagger')(
    swaggerJson
);

// Jira
swaggerJson = require('../services/listJiraPortal/listJiraPortalSwagger')(
    swaggerJson
);
swaggerJson = require('../services/addJiraPortal/addJiraPortalSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/changeStatusJiraPortal/changeStatusPortalSwagger')(
      swaggerJson
  );

// Logs
swaggerJson = require('../services/fetchJiraLogs/fetchJiraLogsSwagger')(
    swaggerJson
);

// Project
swaggerJson = require('../services/listProject/listProjectSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/downloadProjectPersonHoursReport/downloadPersonHoursReportSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/changeStatusProject/changeStatusProjectSwagger')(
      swaggerJson
  );

// reports
swaggerJson =
  require('../services/downloadPersonDayReport/downloadPersonDayReportSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadLogsReport/downloadLogsReportSwagger')(
      swaggerJson
  );

// PLI
swaggerJson = require('../services/employeeProfile/employeeProfileSwagger')(swaggerJson);
swaggerJson = require('../services/allEmployeePli/allEmployeePliSwagger')(swaggerJson);

// Kra
swaggerJson = require('../services/listKra/listKraSwagger')(swaggerJson);
swaggerJson = require('../services/uploadKRA/uploadKRASwagger')(swaggerJson);
swaggerJson = require('../services/getKraForManager/getKraForManagerSwagger')(
    swaggerJson
);
swaggerJson = require('../services/rateKra/rateKraSwagger')(swaggerJson);
swaggerJson = require('../services/getUserKra/getUserKRASwagger')(swaggerJson);
swaggerJson = require('../services/selfKra/selfKraForUserSwagger')(swaggerJson);
swaggerJson = require('../services/rateSelfKra/selfKraSwagger')(swaggerJson);
swaggerJson =
  require('../services/listAssignedKraForAdmin/listAssignedKraForAdminSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadKraAttachment/downloadKraAttachmentSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/deleteKra/deleteKraSwagger')(swaggerJson);
swaggerJson = require('../services/listAssignViewKra/listAssignViewKraSwagger')(
    swaggerJson
);
swaggerJson = require('../services/listKraGroup/listKraGroupSwagger')(
    swaggerJson
);
swaggerJson = require('../services/releaseRatingKra/releaseRatingSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/downloadViewRating/downloadViewRatingSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/unfreezeReviewer/unfreezeReviewerSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/uploadKRAAttachment/uploadKRAAttachmentSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/addCategoryWeightage/addCategoryWeightageSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/notification/notificationSwagger')(
    swaggerJson
);
swaggerJson = require('../services/feedbackMeeting/feedbackMeetingSwagger')(
    swaggerJson
);
swaggerJson = require('../services/addKra/addKraSwagger')(swaggerJson);
swaggerJson =
  require('../services/listCategoryWeightage/listCategoryWeightageSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadUserKraData/downloadUserKraDataSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/startFeedbackMeeting/startFeedbackMeetingSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadPendingKraAssignReport/downloadPendingKraAssignReportSwagger')(
      swaggerJson
  );
swaggerJson =
  require('../services/downloadKraStatusReport/downloadKraStatusReportSwagger')(
      swaggerJson
  );

// PLI Parameters
swaggerJson = require('../services/pliParameters/pliParametersSwagger')(
    swaggerJson
);

// project tracker
swaggerJson = require('../services/jiraProjectList/jiraProjectListSwagger')(
    swaggerJson
);
swaggerJson = require('../services/getProjectTracker/projectTrackerSwagger')(
    swaggerJson
);
swaggerJson =
  require('../services/projectTrackerHealthCard/projectTrackerHealthCardSwagger')(
      swaggerJson
  );
swaggerJson = require('../services/monthlyDeviation/monthlyDeviationSwagger')(
    swaggerJson
);

// Sprint report.
swaggerJson = require('../services/sprintReport/SprintReportSwagger')(
    swaggerJson
);

// Download report.
swaggerJson =
  require('../services/DownloadProjectSprintReport/DownloadProjectSprintReportSwagger')(
      swaggerJson
  );

// Mentee Projects
swaggerJson = require('../services/getProjectsBasedMonth/getProjectSwagger')(
    swaggerJson
);

// Mentee Scores

// PLI Rating
swaggerJson = require('../services/getPliRatingById/getPliRatingByIdSwagger')(swaggerJson);
swaggerJson = require('../services/getPliRatingByEmployeeId/getPliRatingByEmployeeIdSwagger')(swaggerJson);
swaggerJson = require('../services/updateSprintScores/updateSprintScoresSwagger')(
    swaggerJson
);
swaggerJson = require('../services/getPliRatingById/getPliRatingByIdSwagger')(
    swaggerJson
);
// Rag
swaggerJson = require('../services/getRag/ragSwagger')(swaggerJson);

// Project Sprint Data
swaggerJson = require('../services/getProjectSprintData/projectSprintDataSwagger')(swaggerJson);

const baseURL = process.env.BASE_URL ? process.env.BASE_URL.split('://') : ['http', 'localhost:3001'];
swaggerJson.host = baseURL[1];
swaggerJson.info.description = `HostName / URL : ${swaggerJson.host}`;
swaggerJson.schemes[0] = baseURL[0];

module.exports = function (router) {
    router.get('/swagger', (req, res) => {
        res.json(swaggerJson);
    });
    router.use('/api-docs', swaggerUi.serve);
    router.get('/api-docs', swaggerUi.setup(swaggerJson));
};
