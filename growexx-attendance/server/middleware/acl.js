const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const commonAccessList = [
        { method: 'GET', path: '/user/details' },
        { method: 'PUT', path: '/user/picture' },
        { method: 'DELETE', path: '/user/picture' },
        { method: 'GET', path: '/user/attendance' },
        { method: 'GET', path: '/user/attendance/download' },
        { method: 'GET', path: '/jira/logs' },
        { method: 'GET', path: '/project' },
        { method: 'PUT', path: '/project' },
        { method: 'POST', path: '/project' },
        { method: 'GET', path: '/project/all/logs' },
        { method: 'GET', path: '/user/pms' },
        { method: 'GET', path: '/user/members' },
        { method: 'GET', path: '/kra/assessment/manager' },
        { method: 'GET', path: '/kra/assessment/user-kra' },
        { method: 'GET', path: '/kra/self/list' },
        { method: 'PUT', path: '/kra/rate/self/save' },
        { method: 'PUT', path: '/kra/rate/self/freeze' },
        { method: 'POST', path: '/kra/download-ratings' },
        { method: 'GET', path: '/kra/notification' },
        { method: 'PATCH', path: '/kra/user-kra/feedback-meeting-date' },
        { method: 'PATCH', path: '/kra/user-kra/feedback-meeting-done' },
        { method: 'GET', path: '/kra/assigned-to-users/list' },
        { method: 'GET', path: '/project/all' },
        { method: 'GET', path: '/kra/list/view/assign' },
        { method: 'GET', path: '/kra/list/group-by-designation' },
        { method: 'POST', path: '/kra/assign' },
        { method: 'GET', path: '/kra/all' },
        { method: 'POST', path: '/kra/category-weightage' },
        { method: 'GET', path: '/kra/category-weightage' },
        { method: 'POST', path: '/kra' },
        { method: 'GET', path: '/kra' },
        { method: 'DELETE', path: '/kra' },
        { method: 'PUT', path: '/kra' },
        { method: 'POST', path: '/kra/import' },
        { method: 'GET', path: '/kra/list/category-weightage' },
        { method: 'GET', path: '/user' },
        { method: 'GET', path: '/user/person-day/download' },
        { method: 'GET', path: '/project/person-day/download' },
        { method: 'GET', path: '/user/logs/download' },
        { method: 'GET', path: new RegExp('^/pli/employee-profile/\\d+$') }
    ];
    const accessList = {
        1: [
            ...commonAccessList,
            { method: 'PUT', path: '/kra/rate/reporting/save' },
            { method: 'PUT', path: '/kra/rate/reporting/freeze' },
            { method: 'PUT', path: '/kra/rate/reviewer/save' },
            { method: 'PUT', path: '/kra/rate/reviewer/freeze' }
        ],
        2: [
            ...commonAccessList,
            { method: 'PATCH', path: '/project/status' },
            { method: 'PUT', path: '/kra/assessment' },
            { method: 'POST', path: '/kra/publish' },
            { method: 'PUT', path: '/kra/rate/reporting/save' },
            { method: 'PUT', path: '/kra/rate/reporting/freeze' },
            { method: 'PUT', path: '/kra/rate/reviewer/save' },
            { method: 'PUT', path: '/kra/rate/reviewer/freeze' },
            { method: 'GET', path: '/user/billing-sheet/download' },
            { method: 'GET', path: '/project-tracker/project/all' },
            { method: 'GET', path: '/project-tracker/project' },
            { method: 'GET', path: '/project-tracker/sprint' },
            { method: 'GET', path: '/project-tracker/health-cards' },
            { method: 'GET', path: '/project-tracker/download-report' },
            { method: 'GET', path: '/project-tracker/trigger-cron' },
            { method: 'GET', path: '/project-tracker/cron-status' },
            { method: 'POST', path: '/project/person-day/reset-headers' },
            { method: 'GET', path: '/rag/trigger-cron' },
            { method: 'GET', path: '/rag' },
            { method: 'GET', path: '/rag/sprint-metrics' },
            { method: 'GET', path: '/rag/download-report' },
            { method: 'PATCH', path: '/rag' },
            { method: 'POST', path: '/rag/freeze-report' }
        ],
        3: [
            ...commonAccessList,
            { method: 'PATCH', path: '/project/status' },
            { method: 'PUT', path: '/kra' },
            { method: 'PUT', path: '/kra/assessment' },
            { method: 'POST', path: '/kra/publish' },
            { method: 'PUT', path: '/kra/rate/reporting/save' },
            { method: 'PUT', path: '/kra/rate/reporting/freeze' },
            { method: 'PUT', path: '/kra/rate/reviewer/save' },
            { method: 'PUT', path: '/kra/rate/reviewer/freeze' },
            { method: 'GET', path: '/rag' },
            { method: 'GET', path: '/project-tracker/monthly-deviation' }
        ],
        4: [
            ...commonAccessList,
            { method: 'POST', path: '/api/pli-parameters' },
            { method: 'GET', path: '/api/pli-parameters' },
            { method: 'PUT', path: '/api/pli-parameters' },
            { method: 'DELETE', path: '/api/pli-parameters' },
            { method: 'POST', path: '/jira/portal' },
            { method: 'PUT', path: '/jira/portal' },
            { method: 'DELETE', path: '/jira/portal' },
            { method: 'GET', path: '/jira/portals' },
            { method: 'PATCH', path: '/jira/portal/status' },
            { method: 'POST', path: '/user' },
            { method: 'PUT', path: '/user' },
            { method: 'PATCH', path: '/project/status' },
            { method: 'PATCH', path: '/user/role' },
            { method: 'POST', path: '/user/leaves' },
            { method: 'POST', path: '/user/leave' },
            { method: 'PATCH', path: '/user/status' },
            { method: 'POST', path: '/user/designation' },
            { method: 'PUT', path: '/kra/assessment' },
            { method: 'POST', path: '/kra/publish' },
            { method: 'PATCH', path: '/kra/release-ratings' },
            { method: 'PATCH', path: '/kra/user-kra/freezed-by-review-manager' },
            { method: 'PATCH', path: '/kra/assessment/feedback-meeting' },
            { method: 'GET', path: '/kra/download-kra-data' },
            { method: 'GET', path: '/kra/download/pending-assign-kra' },
            { method: 'GET', path: '/kra/download/kra-status' },
            { method: 'POST', path: '/kra/bulk-assign' },
            { method: 'GET', path: '/user/billing-sheet/download' },
            { method: 'GET', path: '/project-tracker/project/all' },
            { method: 'GET', path: '/project-tracker/project' },
            { method: 'GET', path: '/project-tracker/sprint' },
            { method: 'GET', path: '/project-tracker/health-cards' },
            { method: 'GET', path: '/project-tracker/download-report' },
            { method: 'GET', path: '/project-tracker/trigger-cron' },
            { method: 'GET', path: '/project-tracker/cron-status' },
            { method: 'POST', path: '/project/person-day/reset-headers' },
            { method: 'GET', path: '/rag/trigger-cron' },
            { method: 'GET', path: '/rag' },
            { method: 'GET', path: '/rag/sprint-metrics' },
            { method: 'GET', path: '/rag/download-report' },
            { method: 'PATCH', path: '/rag' },
            { method: 'GET', path: '/project-tracker/monthly-deviation' },
            { method: 'GET', path: '/pli/all-employee-profile' }
        ],
        5: [
            ...commonAccessList,
            { method: 'POST', path: '/api/pli-parameters' },
            { method: 'GET', path: '/api/pli-parameters' },
            { method: 'PUT', path: '/api/pli-parameters' },
            { method: 'DELETE', path: '/api/pli-parameters' },
            { method: 'PUT', path: '/kra/bu-head-approved' },
            { method: 'PUT', path: '/kra/rate/reporting/save' },
            { method: 'PUT', path: '/kra/rate/reporting/freeze' },
            { method: 'PUT', path: '/kra/rate/reviewer/save' },
            { method: 'PUT', path: '/kra/rate/reviewer/freeze' },
            { method: 'POST', path: '/jira/portal' },
            { method: 'PUT', path: '/jira/portal' },
            { method: 'DELETE', path: '/jira/portal' },
            { method: 'GET', path: '/jira/portals' },
            { method: 'PATCH', path: '/jira/portal/status' },
            { method: 'POST', path: '/user' },
            { method: 'PUT', path: '/user' },
            { method: 'PATCH', path: '/project/status' },
            { method: 'PATCH', path: '/user/role' },
            { method: 'POST', path: '/user/leaves' },
            { method: 'POST', path: '/user/leave' },
            { method: 'PATCH', path: '/user/status' },
            { method: 'POST', path: '/user/designation' },
            { method: 'PUT', path: '/kra/assessment' },
            { method: 'POST', path: '/kra/publish' },
            { method: 'PATCH', path: '/kra/release-ratings' },
            { method: 'PATCH', path: '/kra/user-kra/freezed-by-review-manager' },
            { method: 'PATCH', path: '/kra/assessment/feedback-meeting' },
            { method: 'GET', path: '/kra/download-kra-data' },
            { method: 'GET', path: '/kra/download/pending-assign-kra' },
            { method: 'GET', path: '/kra/download/kra-status' },
            { method: 'POST', path: '/kra/bulk-assign' },
            { method: 'GET', path: '/project-tracker/project/all' },
            { method: 'GET', path: '/project-tracker/project' },
            { method: 'GET', path: '/project-tracker/sprint' },
            { method: 'GET', path: '/project-tracker/health-cards' },
            { method: 'GET', path: '/project-tracker/download-report' },
            { method: 'GET', path: '/project-tracker/trigger-cron' },
            { method: 'GET', path: '/project-tracker/cron-status' },
            { method: 'GET', path: '/rag/trigger-cron' },
            { method: 'GET', path: '/rag' },
            { method: 'GET', path: '/rag/sprint-metrics' },
            { method: 'PATCH', path: '/rag/sprint-metrics' },
            { method: 'GET', path: '/rag/download-report' },
            { method: 'PATCH', path: '/rag' },
            { method: 'POST', path: '/rag/freeze-report' },
            { method: 'GET', path: '/user/billing-sheet/download' },
            { method: 'POST', path: '/project/person-day/reset-headers' },
            { method: 'GET', path: '/project-tracker/monthly-deviation' }
        ]
    };
    const role = res.locals.user.role;
    const isAllowed = accessList[role].some(route => {
        // console.log('Debug ACL Route:', route);
        // console.log('Debug ACL Request:', req.method);


        // Check if method matches
        if (route.method !== req.method) return false;
        // Check if the path is a RegExp or a string
        if (route.path instanceof RegExp) {
            return route.path.test(req.originalUrl.split('?')[0]);
        }
        // For string paths, do an exact match
        return route.path === req.originalUrl.split('?')[0];
    });

    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
