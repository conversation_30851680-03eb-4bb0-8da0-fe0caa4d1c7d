// Import required modules
const LogService = require('./updateLogService');

class UpdateLogController {
    // Controller method to handle the request to update logStatus
    static async updateLogStatus (req, res) {
        try {
            const { logIds, projectId, logStatus, startDate, endDate } = req.body;
            const userId = res.locals.user._id;
            const role = res.locals.user.role;

            // Validate input
            if (!logIds || !projectId || !([0, 1, 2].includes(logStatus))) {
                return res.status(400).json({ message: 'logId, projectId, and newStatus are required' });
            }

            // Call the service method to update logStatus
            const updatedLog = await LogService.updateLogStatus(logIds, projectId, logStatus, userId, role, startDate, endDate);

            return res.status(200).json({ status: 1, message: 'Log status updated successfully', log: updatedLog });
        } catch (error) {
            return res.status(500).json({ status: 0, message: error.message });
        }
    }

    static async updateBulkLogStatus (req, res) {
        try {
            const { action, token } = req.query;

            const result = await LogService.processBulkAction(action, token);

            if (!result.success) {
                // Redirect with error status and message
                return res.redirect(`${process.env.FRONTEND_URL}/bulk-action?status=error&message=${encodeURIComponent(result.message)}`);
            }

            // Redirect on success
            res.redirect(`${process.env.FRONTEND_URL}/bulk-action?status=success&action=${action}`);
        } catch (error) {
            console.error('Error processing bulk action:', error);
            res.redirect(`${process.env.FRONTEND_URL}/bulk-action?status=error&message=${encodeURIComponent('Internal Server Error')}`);
        }
    }
}

module.exports = UpdateLogController;
