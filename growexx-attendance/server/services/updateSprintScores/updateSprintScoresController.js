const UpdateSprintScoresService = require('./updateSprintScoresService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for updating sprint scores in PLI Rating
 */
class UpdateSprintScoresController {
    /**
   * @desc This function is being used to update sprint scores in PLI rating
   * <AUTHOR>
   * @since 13/05/2025
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
    static async updateSprintScores (req, res) {
        try {
            console.log('Request body:', JSON.stringify(req.body));
            const data = await UpdateSprintScoresService.updateSprintScores(
                req,
                res.__
            );
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            console.error('Error in updateSprintScores:', error);
            console.error('Error stack:', error.stack);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = UpdateSprintScoresController;
