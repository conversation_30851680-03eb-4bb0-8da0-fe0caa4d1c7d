/**
 * @name mentee service
 * <AUTHOR>
 */
const User = require("../../models/user.model");
const MenteeValidator = require("./menteeValidator");
const mongoose = require("mongoose");
const MOMENT = require("moment");
const CONSTANTS = require("../../util/constants");
const Utils = require("../../util/utilFunctions");
const { ObjectId } = mongoose.Types;

/**
 * Class represents service for mentee
 */
class MenteeService {
  /**
   * Default projection properties for mentee queries
   * @returns {Object} Default properties to include in queries
   */
  static defaultMenteeProperties() {
    return {
      employeeId: 1,
      firstName: 1,
      lastName: 1,
      email: 1,
      label: 1,
      role: 1,
      isActive: 1,
      mentorId: 1,
      skills: 1,
      designation: 1,
      level: 1,
    };
  }

  /**
   * @desc This function is being used to get mentees with filtering, searching, and sorting
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @returns {Promise<Object>} Paginated mentees with total count
   */
  static async listMentees(req, user) {
    // Set default options for pagination
    const options = {
      select: MenteeService.defaultMenteeProperties(),
      page: req.query.page ? parseInt(req.query.page) : 1,
      limit: req.query.limit ? parseInt(req.query.limit) : 10,
    };

    // Start building aggregate pipeline
    const aggregateParams = [];

    // Add initial match stage based on the user role
    if (user.role === CONSTANTS.ROLE.HR || user.role === CONSTANTS.ROLE.BU) {
      // HR and BU can see all mentees
      aggregateParams.push({
        $match: {
          menteeId: { $exists: true }, // Only match users that are mentees
        },
      });
    } else {
      // Regular users can only see their mentees
      aggregateParams.push({
        $match: {
          mentorId: user._id,
          menteeId: { $exists: true },
        },
      });
    }

    // Apply active/inactive filter if specified
    if (req.query.isActive !== undefined) {
      aggregateParams[0].$match.isActive = parseInt(req.query.isActive);
    }

    // Add searching capabilities
    if (req.query.name) {
      // Search in firstName and lastName (case-insensitive)
      aggregateParams.push({
        $addFields: {
          fullName: { $concat: ["$firstName", " ", "$lastName"] },
        },
      });
      aggregateParams.push({
        $match: {
          $or: [
            {
              fullName: {
                $regex: `.*${req.query.name}.*`,
                $options: "i",
              },
            },
            {
              email: {
                $regex: `.*${req.query.name}.*`,
                $options: "i",
              },
            },
          ],
        },
      });
    }

    // Add projection stage
    aggregateParams.push({
      $project: {
        ...MenteeService.defaultMenteeProperties(),
        // Create a combined name field for sorting and display
        name: { $concat: ["$firstName", " ", "$lastName"] },
      },
    });

    // Add sorting stages
    aggregateParams.push(...this.getSortingParams(req));

    // Execute aggregation with pagination
    const aggregate = User.aggregate(aggregateParams);
    return await User.aggregatePaginate(aggregate, options);
  }

  /**
   * Generate sorting parameters for aggregation pipeline
   * @param {Object} req Request object
   * @returns {Array} Aggregation stages for sorting
   */
  static getSortingParams(req) {
    const aggregateParams = [];
    const { sort, sortBy } = req.query;

    // Only add sorting if both parameters are provided
    if (sort && sortBy) {
      const sortValue = parseInt(sort);

      // Handle different sortBy fields
      switch (sortBy) {
        case "employeeId":
          aggregateParams.push({
            $sort: { employeeId: sortValue },
          });
          break;
        case "name":
          aggregateParams.push({
            $sort: { name: sortValue },
          });
          break;
        case "email":
          aggregateParams.push({
            $sort: { email: sortValue },
          });
          break;
        case "designation":
          aggregateParams.push({
            $sort: { designation: sortValue },
          });
          break;
        case "menteeId":
          aggregateParams.push({
            $sort: { menteeId: sortValue },
          });
          break;
        default:
          // Default sort by _id if invalid sortBy field is provided
          aggregateParams.push({
            $sort: { _id: sortValue },
          });
      }
    } else {
      // Default sorting if not specified
      aggregateParams.push({
        $sort: { menteeId: -1 },
      });
    }

    return aggregateParams;
  }

  /**
   * @desc Get a specific mentee by ID
   * @param {String} menteeId ID of the mentee to retrieve
   * @param {Object} user Current logged in user
   * @returns {Promise<Object>} Mentee details
   */
  static async getMenteeById(menteeId, user) {
    // Validate mentee ID
    const validator = new MenteeValidator({}, "en");
    validator.validate(menteeId);

    // Build query based on user role
    const query = { _id: menteeId, menteeId: { $exists: true } };

    // If not HR or BU, ensure user can only access their mentees
    if (user.role !== CONSTANTS.ROLE.HR && user.role !== CONSTANTS.ROLE.BU) {
      query.mentorId = user._id;
    }

    // Execute query
    const mentee = await User.findOne(
      query,
      MenteeService.defaultMenteeProperties()
    ).lean();

    if (!mentee) {
      throw new Error("Mentee not found");
    }

    return mentee;
  }

  /**
   * @desc Update a mentee
   * @param {String} menteeId ID of the mentee to update
   * @param {Object} updates Fields to update
   * @param {Object} user Current logged in user
   * @returns {Promise<Object>} Updated mentee
   */
  static async updateMentee(menteeId, updates, user) {
    // Get the current mentee to validate access
    const mentee = await this.getMenteeById(menteeId, user);

    // Only allow certain fields to be updated
    const allowedUpdates = {
      firstName: updates.firstName,
      lastName: updates.lastName,
      email: updates.email,
      skills: updates.skills,
      label: updates.label,
      isActive: updates.isActive,
      designation: updates.designation,
      level: updates.level,
    };

    // Filter out undefined fields
    const filteredUpdates = Object.entries(allowedUpdates)
      .filter(([_, value]) => value !== undefined)
      .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});

    // Perform the update
    const updatedMentee = await User.findByIdAndUpdate(
      menteeId,
      { $set: filteredUpdates },
      { new: true, select: MenteeService.defaultMenteeProperties() }
    ).lean();

    return updatedMentee;
  }

  /**
   * @desc This function is being used to get all mentees for a mentor
   * For backward compatibility with previous implementation
   * <AUTHOR>
   * @param {string} mentorId mentor id
   * @returns {Promise<Array>} mentees
   */
  static async getMentees(mentorId, options = {}) {
    const validator = new MenteeValidator({}, "en");
    validator.validate(mentorId);
    const mentorObjectId = new ObjectId(mentorId);

    const {
      searchText = "",
      filters = {},
      sortBy = "menteeId",
      sortOrder = -1,
      page = 1,
      limit = 10,
    } = options;

    // Construct MongoDB query
    const query = {
      mentorId: mentorObjectId,
      isActive: 1,
      ...filters,
    };

    // Add case-insensitive search on name or email
    if (searchText) {
      query.$or = [
        { firstName: { $regex: searchText, $options: "i" } },
        { lastName: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    // Set up sorting
    const sort = {
      [sortBy]: sortOrder === 1 ? 1 : -1,
    };

    // Set up pagination
    const skip = (page - 1) * limit;

    // Set up counting for pagination meta
    const totalDocs = await User.countDocuments(query);

    // Execute query
    const docs = await User.find(query, MenteeService.defaultMenteeProperties())
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Return with pagination metadata
    return {
      docs,
      totalDocs,
      limit: parseInt(limit),
      page: parseInt(page),
      totalPages: Math.ceil(totalDocs / limit),
      hasNextPage: page * limit < totalDocs,
      hasPrevPage: page > 1,
    };
  }

  /**
   * @desc Assign multiple mentees to a mentor
   * @param {String} mentorId ID of the mentor
   * @param {Array<String>} menteeIds Array of mentee IDs to assign
   * @param {Object} user User object (optional)
   * @param {Boolean} overwriteExisting If true, existing mentor assignments will be overwritten
   * @returns {Promise<Object>} Result of the assignment process
   */
  static async assignMenteesToMentor(
    mentorId,
    menteeIds,
    user = null,
    overwriteExisting = true
  ) {
    // Validate request data
    const validator = new MenteeValidator({}, "en");
    validator.validateAssignMentees({ mentorId, menteeIds });

    // Validate mentor exists
    const mentor = await User.findById(mentorId);
    if (!mentor) {
      throw new Error("Mentor not found");
    }

    // Remove duplicate mentee IDs
    const uniqueMenteeIds = [...new Set(menteeIds)];

    // Get all mentees
    const mentees = await User.find({ _id: { $in: uniqueMenteeIds } });

    // Track mentees with different mentors for reassignment
    const menteesWithDifferentMentor = [];
    const menteesWithSameMentor = [];
    const menteesWithNoMentor = [];

    // Categorize mentees
    for (const mentee of mentees) {
      if (!mentee.mentorId) {
        menteesWithNoMentor.push(mentee);
      } else if (mentee.mentorId.toString() === mentorId) {
        menteesWithSameMentor.push(mentee);
      } else {
        menteesWithDifferentMentor.push(mentee);
      }
    }

    // Check if any mentee is a mentor of the assigned mentor (circular mentorship)
    for (const mentee of mentees) {
      // Check if the mentee is a mentor of the assigned mentor
      const isMentorOfMentor = await User.findOne({
        _id: mentorId,
        mentorId: mentee._id,
        isActive: 1,
      });

      if (isMentorOfMentor) {
        const error = new Error("MENTEE_IS_MENTOR_OF_MENTOR");
        error.status = 400;
        throw error;
      }
    }

    // If overwriteExisting is false, we should throw an error if any mentee has a different mentor
    if (!overwriteExisting && menteesWithDifferentMentor.length > 0) {
      const mentee = menteesWithDifferentMentor[0];
      throw new Error(
        `User ${mentee.firstName} ${mentee.lastName} is already assigned to a different mentor`
      );
    }

    // Determine which mentees need to be updated
    const menteesToUpdate = [
      ...menteesWithNoMentor,
      ...(overwriteExisting ? menteesWithDifferentMentor : []),
    ];

    // If no mentees need to be updated, return early
    if (menteesToUpdate.length === 0) {
      return {
        mentorId,
        assignedCount: 0,
        totalMentees: menteeIds.length,
        skippedDuplicates: menteeIds.length - uniqueMenteeIds.length,
        skippedAlreadyAssigned: menteesWithSameMentor.length,
        reassignedMentees: [],
      };
    }

    // Update mentees with the new mentor
    const menteesToUpdateIds = menteesToUpdate.map((mentee) => mentee._id);
    const updateResult = await User.updateMany(
      { _id: { $in: menteesToUpdateIds } },
      { $set: { mentorId: ObjectId(mentorId) } }
    );

    // Prepare reassigned mentees info
    const reassignedMentees = menteesWithDifferentMentor.map((mentee) => ({
      menteeId: mentee._id,
      menteeEmail: mentee.email,
      menteeFullName: `${mentee.firstName} ${mentee.lastName}`,
      previousMentorId: mentee.mentorId,
      newMentorId: mentorId,
    }));

    return {
      mentorId,
      assignedCount: updateResult.modifiedCount,
      totalMentees: menteeIds.length,
      skippedDuplicates: menteeIds.length - uniqueMenteeIds.length,
      skippedAlreadyAssigned: menteesWithSameMentor.length,
      reassignedMentees: reassignedMentees,
    };
  }

  /**
   * @desc Process a file containing mentor-mentee assignments
   * @param {Object} file The uploaded file object from multer
   * @param {Object} user Current logged in user
   * @returns {Promise<Object>} Results of the processing operation
   */
  static async processMentorMenteeFile(file, user) {
    try {
      // Check if file exists and has content
      if (!file || !file.buffer) {
        throw new Error("Invalid file data");
      }

      // Parse the file content as JSON
      let mentorMenteeData;
      try {
        const fileContent = file.buffer.toString("utf8");
        mentorMenteeData = JSON.parse(fileContent);
      } catch (error) {
        throw new Error("Invalid JSON format in the uploaded file");
      }

      // Validate the structure of the data
      if (!Array.isArray(mentorMenteeData)) {
        throw new Error(
          "File must contain an array of mentor-mentee assignments"
        );
      }

      // Process each row in the file
      const results = {
        totalRows: mentorMenteeData.length,
        successfulAssignments: 0,
        skippedRows: 0,
        skippedEmails: [],
        errors: [],
      };

      // Get all organization emails for validation
      const allOrgUsers = await User.find({}, { email: 1 }).lean();
      const orgEmails = new Set(
        allOrgUsers.map((user) => user.email.toLowerCase())
      );

      // Process each row
      for (const row of mentorMenteeData) {
        try {
          // Validate row structure
          if (
            !row["Mentor email"] ||
            !row["Mentee email"] ||
            !Array.isArray(row["Mentee email"])
          ) {
            results.errors.push(`Invalid row format: ${JSON.stringify(row)}`);
            results.skippedRows++;
            continue;
          }

          const mentorEmail = row["Mentor email"].toLowerCase();
          const menteeEmails = row["Mentee email"].map((email) =>
            email.toLowerCase()
          );

          // Check if mentor email exists in organization
          if (!orgEmails.has(mentorEmail)) {
            results.skippedEmails.push(mentorEmail);
            results.errors.push(
              `Mentor email not found in organization: ${mentorEmail}`
            );
            results.skippedRows++;
            continue;
          }

          // Filter out mentee emails that don't exist in organization
          const validMenteeEmails = menteeEmails.filter((email) =>
            orgEmails.has(email)
          );
          const invalidMenteeEmails = menteeEmails.filter(
            (email) => !orgEmails.has(email)
          );

          // If any mentee email is invalid, skip the entire row
          if (invalidMenteeEmails.length > 0) {
            // Add invalid emails to skipped list with detailed reason
            invalidMenteeEmails.forEach((email) => {
              results.skippedEmails.push(email);
              results.errors.push({
                email,
                reason: `Mentee email not found in organization (for mentor: ${mentorEmail})`,
              });
            });

            results.errors.push({
              email: mentorEmail,
              reason: `Row skipped due to invalid mentee email(s) for mentor: ${mentorEmail}`,
            });
            results.skippedRows++;
            continue;
          }

          // If we reach here, all mentee emails are valid
          // Get the mentor's ObjectId
          const mentor = await User.findOne(
            { email: mentorEmail },
            { _id: 1 }
          ).lean();
          if (!mentor) {
            results.errors.push(`Mentor not found in database: ${mentorEmail}`);
            results.skippedRows++;
            continue;
          }

          // Get mentees' ObjectIds
          const mentees = await User.find(
            { email: { $in: validMenteeEmails } },
            { _id: 1, email: 1, mentorId: 1 }
          ).lean();

          // Get IDs of mentees that don't already have a different mentor
          const menteeIds = mentees
            .filter(
              (mentee) =>
                !mentee.mentorId ||
                mentee.mentorId.toString() === mentor._id.toString()
            )
            .map((mentee) => mentee._id);

          // Skip if no valid mentees to assign
          if (menteeIds.length === 0) {
            results.errors.push({
              email: mentorEmail,
              reason: `No valid mentees to assign for mentor: ${mentorEmail}`,
            });
            results.skippedRows++;
            continue;
          }

          // Assign mentees to mentor
          const assignmentResult = await this.assignMenteesToMentor(
            mentor._id.toString(),
            menteeIds.map((id) => id.toString()),
            user
          );

          // Update results
          results.successfulAssignments += assignmentResult.assignedCount;
        } catch (error) {
          results.errors.push(`Error processing row: ${error.message}`);
          results.skippedRows++;
        }
      }

      return results;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @desc Process mentor-mentee assignments from JSON data
   * @param {Array} mentorMenteeData Array of mentor-mentee assignment objects
   * @param {Object} user Current logged in user
   * @param {Boolean} overwriteExisting If true, existing mentor assignments will be overwritten
   * @returns {Promise<Object>} Results of the processing operation
   */
  static async processMentorMenteeAssignments(
    mentorMenteeData,
    user = null,
    overwriteExisting = true
  ) {
    try {
      // Validate the structure of the data
      if (!Array.isArray(mentorMenteeData)) {
        throw new Error(
          "Invalid data format. Expected an array of mentor-mentee assignments"
        );
      }

      // Process each row in the data
      const results = {
        totalRows: mentorMenteeData.length,
        successfulAssignments: 0,
        skippedRows: 0,
        skippedEmails: [],
        errors: [],
        partiallyProcessedRows: 0,
      };

      // Get all organization emails for validation
      const allOrgUsers = await User.find({}, { email: 1 }).lean();
      const orgEmails = new Set(
        allOrgUsers.map((user) => user.email.toLowerCase())
      );

      // Process each row
      for (const row of mentorMenteeData) {
        try {
          // Validate row structure
          if (
            !row["Mentor email"] ||
            !row["Mentee email"] ||
            !Array.isArray(row["Mentee email"])
          ) {
            results.errors.push(`Invalid row format: ${JSON.stringify(row)}`);
            results.skippedRows++;
            continue;
          }

          const mentorEmail = row["Mentor email"].toLowerCase();
          const menteeEmails = row["Mentee email"].map((email) =>
            email.toLowerCase()
          );

          // Check if mentor email exists in organization
          if (!orgEmails.has(mentorEmail)) {
            results.skippedEmails.push(mentorEmail);
            results.errors.push({
              email: mentorEmail,
              reason: `Mentor email not found in organization: ${mentorEmail}`,
            });
            results.skippedRows++;
            continue;
          }

          // Filter out mentee emails that don't exist in organization
          const validMenteeEmails = menteeEmails.filter((email) =>
            orgEmails.has(email)
          );
          const invalidMenteeEmails = menteeEmails.filter(
            (email) => !orgEmails.has(email)
          );

          // Track invalid mentee emails but continue processing valid ones
          if (invalidMenteeEmails.length > 0) {
            // Add invalid emails to skipped list with detailed reason
            invalidMenteeEmails.forEach((email) => {
              results.skippedEmails.push(email);
              results.errors.push({
                email,
                reason: `Mentee email not found in organization (for mentor: ${mentorEmail})`,
              });
            });

            // If all mentee emails are invalid, skip the row
            if (validMenteeEmails.length === 0) {
              results.errors.push({
                email: mentorEmail,
                reason: `Row skipped due to all mentee emails being invalid for mentor: ${mentorEmail}`,
              });
              results.skippedRows++;
              continue;
            }

            // Otherwise, mark as partially processed
            results.partiallyProcessedRows++;
          }

          // If we reach here, at least some mentee emails are valid
          // Get the mentor's ObjectId
          const mentor = await User.findOne(
            { email: mentorEmail },
            { _id: 1 }
          ).lean();
          if (!mentor) {
            results.errors.push({
              email: mentorEmail,
              reason: `Mentor not found in database: ${mentorEmail}`,
            });
            results.skippedRows++;
            continue;
          }

          // Get mentees' ObjectIds
          const mentees = await User.find(
            { email: { $in: validMenteeEmails } },
            { _id: 1, email: 1, mentorId: 1 }
          ).lean();

          // Get IDs of all mentees (when overwriteExisting is true) or only those without a different mentor
          const menteeIds = overwriteExisting
            ? mentees.map((mentee) => mentee._id) // Include all mentees when overwriteExisting is true
            : mentees
                .filter(
                  (mentee) =>
                    !mentee.mentorId ||
                    mentee.mentorId.toString() === mentor._id.toString()
                )
                .map((mentee) => mentee._id);

          // Skip if no valid mentees to assign
          if (menteeIds.length === 0) {
            results.errors.push({
              email: mentorEmail,
              reason: `No valid mentees to assign for mentor: ${mentorEmail}`,
            });
            results.skippedRows++;
            continue;
          }

          // Assign mentees to mentor
          const assignmentResult = await this.assignMenteesToMentor(
            mentor._id.toString(),
            menteeIds.map((id) => id.toString()),
            user,
            overwriteExisting
          );

          // Update results
          results.successfulAssignments += assignmentResult.assignedCount;
        } catch (error) {
          results.errors.push({
            reason: `Error processing row: ${error.message}`,
          });
          results.skippedRows++;
        }
      }

      return results;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @desc This function is being used to get all mentees grouped by their mentors
   * <AUTHOR>
   * @returns {Promise<Array>} Array of mentors with their mentees
   */
  static async getMenteesGroupedByMentor() {
    try {
      // Find all users who have mentees (they are mentors)
      const mentorsWithMentees = await User.aggregate([
        // Stage 1: Find all users who are referenced as mentors
        {
          $match: {
            mentorId: { $ne: null },
            isActive: 1,
          },
        },
        // Stage 2: Group mentees by their mentorId
        {
          $group: {
            _id: "$mentorId",
            mentees: {
              $push: {
                id: "$_id",
                name: { $concat: ["$firstName", " ", "$lastName"] },
                email: "$email",
                designation: "$designation",
                empId: "$employeeId",
              },
            },
          },
        },
        // Stage 3: Lookup mentor details
        {
          $lookup: {
            from: "users",
            localField: "_id",
            foreignField: "_id",
            as: "mentorDetails",
          },
        },
        // Stage 4: Unwind the mentor details array
        {
          $unwind: "$mentorDetails",
        },
        // Stage 5: Project the final output format
        {
          $project: {
            id: "$_id",
            mentor: {
              $concat: [
                "$mentorDetails.firstName",
                " ",
                "$mentorDetails.lastName",
              ],
            },
            department: "$mentorDetails.businessUnit",
            empId: "$mentorDetails.employeeId",
            mentees: 1,
            _id: 0,
          },
        },
      ]);

      return mentorsWithMentees;
    } catch (error) {
      throw new Error(
        `Error retrieving mentees grouped by mentor: ${error.message}`
      );
    }
  }
}

module.exports = MenteeService;
