/**
 * @name mentee controller
 * <AUTHOR>
 */
const MenteeService = require('./menteeService');
const Utils = require('../../util/utilFunctions');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents controller for mentee
 */
class MenteeController {
    /**
   * @desc This function is being used to get mentees with pagination
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async listMentees (req, res) {
        try {
            const user = res.locals.user;
            const mentees = await MenteeService.listMentees(req, user);
            Utils.sendResponse(null, mentees, res, res.__('MENTEES_FETCHED'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
   * @desc This function is being used to get mentees (legacy endpoint)
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async getMentees (req, res) {
        try {
            const user = res.locals.user;

            // // Check if user has permission to access mentees
            // if (user.role !== CONSTANTS.ROLE.ADMIN && user.role !== CONSTANTS.ROLE.HR) {
            //   return res.status(403).json({ message: 'Insufficient permissions' });
            // }

            const mentorId = req.query._id || user._id;

            if (!mentorId) {
                return res.status(400).json({ message: 'Invalid request parameters' });
            }
            // Build options for filtering, sorting, and pagination
            const options = {
                searchText: req.query.name || '',
                sortBy: req.query.sortBy || 'menteeId',
                sortOrder: req.query.sort ? parseInt(req.query.sort) : -1,
                page: req.query.page ? parseInt(req.query.page) : 1,
                limit: req.query.limit ? parseInt(req.query.limit) : 10
            };

            // Add isActive filter if provided
            if (req.query.isActive !== undefined) {
                options.filters = { isActive: parseInt(req.query.isActive) };
            }

            const mentees = await MenteeService.getMentees(mentorId, options);
            Utils.sendResponse(null, mentees, res, res.__('MENTEES_FETCHED'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
   * @desc This function is being used to get a specific mentee by ID
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async getMenteeById (req, res) {
        try {
            const menteeId = req.params.id;
            const user = res.locals.user;
            const mentee = await MenteeService.getMenteeById(menteeId, user);
            Utils.sendResponse(null, mentee, res, res.__('MENTEE_FETCHED'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
   * @desc This function is being used to update a mentee
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async updateMentee (req, res) {
        try {
            const menteeId = req.params.id;
            const updates = req.body;
            const user = res.locals.user;

            const updatedMentee = await MenteeService.updateMentee(
                menteeId,
                updates,
                user
            );
            Utils.sendResponse(null, updatedMentee, res, res.__('MENTEE_UPDATED'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
 * @desc This function is being used to assign multiple mentees to a mentor
 * <AUTHOR>
 * @param {Object} req Request
 * @param {Object} res Response
 */
    static async assignMenteesToMentor (req, res) {
        try {
            const { mentorId, menteeIds } = req.body;

            if (!mentorId || !menteeIds || !Array.isArray(menteeIds)) {
                return Utils.sendResponse(new Error('Invalid request parameters'), null, res, '');
            }

            const result = await MenteeService.assignMenteesToMentor(mentorId, menteeIds);
            return Utils.sendResponse(null, result, res, 'Mentees assigned successfully');

        } catch (error) {
            if (error.message === 'MENTEE_IS_MENTOR_OF_MENTOR') {
                return res.status(400).json({ message: error.message });
            }

            if (error.message.includes('already assigned to a different mentor')) {
                return Utils.sendResponse(
                    new Error('MENTEE_ALREADY_ASSIGNED_TO_OTHER'),
                    null,
                    res,
                    ''
                );
            }

            return Utils.sendResponse(error, null, res, '');
        }
    }


    /**
   * @desc This function is being used to process mentor-mentee assignments from JSON data or file
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async processMentorMenteeAssignments (req, res) {
        try {
            // const user = res.locals.user;

            // // Check if user has admin permissions
            // if (
            //   user.role !== CONSTANTS.ROLE.ADMIN &&
            //   user.role !== CONSTANTS.ROLE.HR
            // ) {
            //   throw new Error("INSUFFICIENT_PERMISSIONS");
            // }

            let mentorMenteeData;

            // Check if this is a file upload or direct JSON data
            if (req.file) {
                // Process file upload
                try {
                    const fileContent = req.file.buffer.toString('utf8');
                    mentorMenteeData = JSON.parse(fileContent);
                } catch (error) {
                    throw new Error('Invalid JSON format in the uploaded file');
                }
            } else {
                // Process direct JSON data
                mentorMenteeData = req.body;
            }

            // Validate the data
            if (!Array.isArray(mentorMenteeData) || mentorMenteeData.length === 0) {
                throw new Error('INVALID_DATA_FORMAT');
            }

            // Get the overwriteExisting flag from query parameters
            // Default to true if not specified
            const overwriteExisting = req.query.overwriteExisting !== 'false';

            const result = await MenteeService.processMentorMenteeAssignments(
                mentorMenteeData,
                // user,
                overwriteExisting
            );

            // Create a more detailed response with information about skipped mentees
            const responseData = {
                ...result,
                message: res.__('MENTOR_MENTEE_ASSIGNMENT_PROCESSED'),
                summary: `Processed ${result.totalRows} rows: ${result.successfulAssignments} successful assignments, ${result.skippedRows} skipped.`
            };

            // If we have skipped emails, add a note about them
            if (result.skippedEmails && result.skippedEmails.length > 0) {
                responseData.skippedEmailsCount = result.skippedEmails.length;
                responseData.summary += ` ${result.skippedEmails.length} emails skipped.`;
            }

            // If we have partially processed rows, add a note about them
            if (result.partiallyProcessedRows && result.partiallyProcessedRows > 0) {
                responseData.partiallyProcessedRowsCount =
          result.partiallyProcessedRows;
                responseData.summary += ` ${result.partiallyProcessedRows} rows were partially processed (some mentees were valid, some were not).`;
            }

            Utils.sendResponse(
                null,
                responseData,
                res,
                res.__('MENTOR_MENTEE_ASSIGNMENT_PROCESSED')
            );
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
   * @desc This function is being used to get all mentees grouped by their mentors
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async getMenteesGroupedByMentor (req, res) {
        try {
            // This endpoint is public and doesn't require authentication
            console.log('Calling MenteeService.getMenteesGroupedByMentor()');
            const result = await MenteeService.getMenteesGroupedByMentor();
            console.log('Result', result);

            console.log('Sending response with Utils.sendResponse');
            Utils.sendResponse(null, result, res, res.__('SUCCESS'));
        } catch (error) {
            console.log('Error in getMenteesGroupedByMentor:', error.message);
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = MenteeController;
