const PliRating = require('../../models/pliRating.model');
const Project = require('../../models/project.model');
const PliParameter = require('../../models/pliParameters.model');
const User = require('../../models/user.model');
const mongoose = require('mongoose');
const RAG = require('../../models/rag.model');

exports.getMenteeProjects = async ({ menteeId, month, year }) => {
    try {
    // First, find the user by employeeId if it's not an ObjectId
        let userObjectId;

        if (mongoose.Types.ObjectId.isValid(menteeId)) {
            userObjectId = new mongoose.Types.ObjectId(menteeId);
        } else {
            // Find user by employeeId
            const user = await User.findOne({ employeeId: parseInt(menteeId) });
            if (!user) {
                throw new Error('User not found with the given Employee ID');
            }
            userObjectId = user._id;
        }

        // Step 1: Fetch from PLI Rating Schema
        const pliRating = await PliRating.findOne({
            menteeId: userObjectId,
            month: parseInt(month),
            year: parseInt(year)
        }).lean();

        let ratedProjectIds = [];
        let ratedProjects = [];

        if (
            pliRating &&
      pliRating.projectRatings &&
      pliRating.projectRatings.length > 0
        ) {
            // Get projects that are already rated
            ratedProjectIds = pliRating.projectRatings.map((r) =>
                r.projectId.toString()
            );

            ratedProjects = await Project.find({
                _id: {
                    $in: ratedProjectIds.map((id) => new mongoose.Types.ObjectId(id))
                },
                isDelete: 0
            }).lean();
        } else {
            // Step 2: If no PLI Rating data, fetch from Project model
            const startDate = new Date(year, month - 1, 1);
            const endDate = new Date(year, month, 0, 23, 59, 59, 999);

            ratedProjects = await Project.find({
                users: {
                    $elemMatch: {
                        empId: userObjectId,
                        startDate: { $lte: endDate },
                        $or: [
                            { endDate: { $exists: false } },
                            { endDate: { $gte: startDate } }
                        ]
                    }
                },
                isDelete: 0
            }).lean();

            ratedProjectIds = ratedProjects.map((p) => p._id.toString());
        }

        // Step 3: Get additional projects (projects not in rated list)
        const allMenteeProjects = await Project.find({
            users: {
                $elemMatch: {
                    empId: userObjectId
                }
            },
            isDelete: 0
        }).lean();

        const additionalProjects = allMenteeProjects.filter(
            (p) => !ratedProjectIds.includes(p._id.toString())
        );

        // Step 4: Map project types using PLI Parameters
        const pliParams = await PliParameter.find({ isActive: 1 }).lean();

        const mapProjectToType = (projectName) => {
            const param = pliParams.find((p) => p.parentParameter === projectName);
            return param ? param.projectType : null;
        };

        // Format projects with their types
        const formatProject = (project) => ({
            _id: project._id,
            projectName: project.projectName,
            projectType: mapProjectToType(project.projectName)
        });

        // Step 5: Return formatted response
        return {
            ratedProjects: ratedProjects.map(formatProject),
            additionalProjects: additionalProjects.map(formatProject)
        };
    } catch (error) {
        throw error;
    }
};

exports.getMenteeProjectsByLabel = async ({ menteeLabel, month, year }) => {
    try {
        if (!menteeLabel) {
            throw new Error('Mentee label is required');
        }


        // Find projects from RAG model where team.member matches menteeLabel
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);

        // Find RAG entries where the team member matches the menteeLabel
        // and the sprint end date falls within the selected month
        const ragEntries = await RAG.find({
            'team.member': menteeLabel,
            sprintEnd: { $gte: startDate, $lte: endDate }
        }).lean();

        // Extract project names from RAG entries
        const projectNames = [...new Set(ragEntries.map(entry => entry.project))];

        // Find projects in Project model that match the project names
        const projects = await Project.find({
            projectName: { $in: projectNames },
            isDelete: 0
        }).lean();

        // Step 4: Map project types using PLI Parameters
        const pliParams = await PliParameter.find({ isActive: 1 }).lean();

        const mapProjectToType = (projectName) => {
            const param = pliParams.find((p) => p.parentParameter === projectName);
            return param ? param.projectType : null;
        };

        // Format projects with their types
        const formatProject = (project) => ({
            _id: project._id,
            projectName: project.projectName,
            projectType: mapProjectToType(project.projectName),
            source:'RAG'
        });

        // Return formatted response with all projects in rated projects field
        return {
            ratedProjects: projects.map(formatProject),
            additionalProjects: []
        };
    } catch (error) {
        throw error;
    }
};

exports.syncProjectWeights = async ({
    menteeId,
    mentorId,
    month,
    year,
    projects
}) => {
    try {
    // Find user by employeeId if it's not an ObjectId
        let menteeObjectId;
        let mentorObjectId;

        // Handle menteeId
        if (mongoose.Types.ObjectId.isValid(menteeId)) {
            menteeObjectId = new mongoose.Types.ObjectId(menteeId);
        } else {
            // Find user by employeeId
            const mentee = await User.findOne({ employeeId: parseInt(menteeId) });
            if (!mentee) {
                throw new Error('Mentee not found with the given Employee ID');
            }
            menteeObjectId = mentee._id;
        }

        // Handle mentorId
        if (mongoose.Types.ObjectId.isValid(mentorId)) {
            mentorObjectId = new mongoose.Types.ObjectId(mentorId);
        } else {
            // Find user by employeeId
            const mentor = await User.findOne({ employeeId: parseInt(mentorId) });
            if (!mentor) {
                throw new Error('Mentor not found with the given Employee ID');
            }
            mentorObjectId = mentor._id;
        }

        // Find or create PLI rating
        let pliRating = await PliRating.findOne({
            menteeId: menteeObjectId,
            month: parseInt(month),
            year: parseInt(year)
        });

        // Get PLI parameters for project types
        const pliParams = await PliParameter.find({ isActive: 1 }).lean();

        // Get project details
        const projectDetails = await Project.find({
            _id: {
                $in: projects.map((p) => new mongoose.Types.ObjectId(p.projectId))
            }
        }).lean();

        // Map project ratings with parameters
        const projectRatings = projects.map((proj) => {
            const projectDetail = projectDetails.find(
                (p) => p._id.toString() === proj.projectId
            );
            const pliParam = pliParams.find(
                (p) => p.parentParameter === projectDetail.projectName
            );

            return {
                projectId: new mongoose.Types.ObjectId(proj.projectId),
                projectWeightage: proj.projectWeightage,
                parameterScores: pliParam
                    ? pliParam.childParameters.map((param) => ({
                        parameterId: pliParam._id,
                        autoFilled: true,
                        score: 0,
                        comments: '',
                        childScores: param.childParameters
                            ? param.childParameters.map((child) => ({
                                childParameterId: child._id,
                                score: 0
                            }))
                            : []
                    }))
                    : []
            };
        });

        if (!pliRating) {
            // Create new PLI rating with all required fields
            pliRating = new PliRating({
                menteeId: menteeObjectId,
                mentorId: mentorObjectId,
                month: parseInt(month),
                year: parseInt(year),
                projectRatings: projectRatings,
                status: 'Draft',
                proactivenessScores: [
                    { criteria: 'Meeting Deadlines', score: 0 },
                    { criteria: 'Task Prioritization', score: 0 },
                    { criteria: 'Time Tracking', score: 0 },
                    { criteria: 'Initiative', score: 0 },
                    { criteria: 'Issue Identification', score: 0 },
                    { criteria: 'Solution Proposals', score: 0 },
                    { criteria: 'Stakeholder Communication', score: 0 },
                    { criteria: 'Adaptability', score: 0 }
                ],
                techRoadmapRating: 0,
                isFrozen: false,
                superAdminOverride: false
            });
        } else {
            // Update existing PLI rating
            pliRating.projectRatings = projectRatings;
            pliRating.status = 'Draft';
            pliRating.updatedAt = new Date();
        }

        await pliRating.save();

        // Return populated data
        return await PliRating.findById(pliRating._id)
            .populate('projectRatings.projectId')
            .populate('menteeId', 'name email employeeId')
            .populate('mentorId', 'name email employeeId')
            .lean();
    } catch (error) {
        throw error;
    }
};
