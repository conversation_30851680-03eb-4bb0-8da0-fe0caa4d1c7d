const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for Project Sprint Data
 */
class ProjectSprintDataValidator extends validation {
    constructor (query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc Validate required parameters are present
     * @param {Object} params Parameters to validate
     * @param {Array} requiredFields List of required field names
     */
    validateRequiredParams (params, requiredFields) {
        for (const field of requiredFields) {
            if (!params[field]) {
                const fieldLabel = field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1').trim();
                throw new GeneralError(this.__(this.REQUIRED, fieldLabel), 400);
            }
        }
    }

    /**
     * @desc Validate month value is between 1-12
     * @param {String|Number} month Month value to validate
     */
    validateMonth (month) {
        const monthNum = parseInt(month, 10);
        if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
            throw new GeneralError(this.__('INVALID_MONTH'), 400);
        }
    }

    /**
     * @desc Validate year is in reasonable range
     * @param {String|Number} year Year value to validate
     */
    validateYear (year) {
        const yearNum = parseInt(year, 10);
        if (isNaN(yearNum) || yearNum < 2000 || yearNum > 2100) {
            throw new GeneralError(this.__('INVALID_YEAR'), 400);
        }
    }

    /**
     * @desc Validate project type is one of the allowed values
     * @param {String} projectType Project type to validate
     */
    validateProjectType (projectType) {
        if (!['Dedicated', 'Fixed'].includes(projectType)) {
            throw new GeneralError(this.__('INVALID_PROJECT_TYPE'), 400);
        }
    }

    /**
     * @desc This function is being used to validate the project sprint data request
     * <AUTHOR>
     * @since 08/21/2024
     * @param {Object} query The query parameters
     */
    validateProjectSprintDataParams (query) {
        const { month, year, projectType } = query;

        // Validate all required parameters
        this.validateRequiredParams(query, ['project', 'month', 'year', 'employeeName', 'projectType']);

        // Validate numeric and enum values
        this.validateMonth(month);
        this.validateYear(year);
        this.validateProjectType(projectType);
    }
}

module.exports = ProjectSprintDataValidator;
