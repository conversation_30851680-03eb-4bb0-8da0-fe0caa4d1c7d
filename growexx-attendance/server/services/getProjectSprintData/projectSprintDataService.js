const RagModel = require('../../models/rag.model');
const SprintMetricsModel = require('../../models/sprintMetrics.model');
const PLIParametersModel = require('../../models/pliParameters.model');
const PLIRatingModel = require('../../models/pliRating.model');
const RagService = require('../getRag/ragService');
const UserModel = require('../../models/user.model');

/**
 * Class represents services for Project Sprint Data
 */
class ProjectSprintDataService {
    /**
   * @desc This function maps specific designations to general role categories
   * <AUTHOR>
   * @since 05/04/2025
   * @param {String} designation The specific designation from constants
   * @return {String} The general role category for PLI parameters
   */
    static mapDesignationToRole (designation) {
    // Default role if no mapping is found
        let role = 'Developers';

        if (!designation) {
            return role;
        }

        // Convert designation to lowercase for case-insensitive matching
        const lowerDesignation = designation.toLowerCase();

        // Developer roles
        if (
            lowerDesignation.includes('software engineer') ||
      lowerDesignation.includes('data engineer') ||
      lowerDesignation.includes('data scientist') ||
      lowerDesignation.includes('database administrator') ||
      lowerDesignation.includes('web analytics engineer') ||
      lowerDesignation.includes('data analyst')
        ) {
            role = 'Developers';
        } else if (
            lowerDesignation.includes('qa') ||
      lowerDesignation.includes('quality assurance')
        ) {
            role = 'QA';
        } else if (
            lowerDesignation.includes('technical lead') ||
      lowerDesignation.includes('lead engineer') ||
      lowerDesignation.includes('lead - data') ||
      lowerDesignation.includes('sr. technical lead')
        ) {
            role = 'TL';
        } else if (
            lowerDesignation.includes('technical project manager') ||
      lowerDesignation.includes('scrum master')
        ) {
            role = 'TPM';
        } else if (
            lowerDesignation.includes('project manager') ||
      lowerDesignation.includes('product manager') ||
      lowerDesignation.includes('product owner') ||
      lowerDesignation.includes('service delivery manager')
        ) {
            role = 'PM';
        } else if (
            lowerDesignation.includes('hr') ||
      lowerDesignation.includes('talent acquisition') ||
      lowerDesignation.includes('human resource')
        ) {
            role = 'HR';
        } else if (
            lowerDesignation.includes('manager') ||
      lowerDesignation.includes('head') ||
      lowerDesignation.includes('director') ||
      lowerDesignation.includes('co-founder')
        ) {
            role = 'PM';
        } else if (
            lowerDesignation.includes('business analyst') ||
      lowerDesignation.includes('analyst')
        ) {
            role = 'Developers';
        }

        return role;
    }

    /**
     * @desc Find user by username and get their ID
     * @param {String} username The username to search for
     * @return {Object} Object containing menteeId and any error
     */
    static async findUserByUsername (username) {
        try {
            const user = await UserModel.findOne({ label: { $in: [username] } }).lean();
            return {
                menteeId: user && user._id ? user._id : null,
                error: null
            };
        } catch (error) {
            return { menteeId: null, error: error.message };
        }
    }

    /**
     * @desc Find project by name
     * @param {String} projectName The project name to search for
     * @return {Object} Object containing projectId and any error
     */
    static async findProjectByName (projectName) {
        try {
            const ProjectModel = require('../../models/project.model');
            const projectDoc = await ProjectModel.findOne({
                projectName: projectName,
                isActive: 1,
                isDelete: 0
            }).lean();

            return {
                projectId: projectDoc && projectDoc._id ? projectDoc._id : null,
                error: null
            };
        } catch (error) {
            return { projectId: null, error: error.message };
        }
    }

    /**
     * @desc Find existing PLI rating for a mentee in a specific month and year
     * @param {String} menteeId The mentee ID
     * @param {Number} month The month number
     * @param {Number} year The year number
     * @param {String} projectId The project ID
     * @return {Object} Object containing existing PLI rating data
     */
    static async findExistingPLIRating (menteeId, month, year, projectId) {
        try {
            if (!menteeId) {
                return {
                    existingPLIRating: null,
                    existingProjectRating: null
                };
            }

            // Find PLI Rating for this mentee, month, and year
            const existingPLIRating = await PLIRatingModel.findOne({
                menteeId,
                month: parseInt(month),
                year: parseInt(year)
            }).lean();

            let existingProjectRating = null;

            // If PLI Rating exists, find the project rating for this project
            if (existingPLIRating && existingPLIRating.projectRatings && existingPLIRating.projectRatings.length > 0) {
                // Find the project by ID
                if (projectId) {
                    existingProjectRating = existingPLIRating.projectRatings.find(pr =>
                        pr.projectId && pr.projectId.toString() === projectId.toString()
                    );
                }

                // If we couldn't find by ID (maybe because project ID changed), try a fallback
                if (!existingProjectRating && existingPLIRating.projectRatings.length === 1) {
                    // If there's only one project rating, use it as a fallback
                    existingProjectRating = existingPLIRating.projectRatings[0];
                }
            }

            return { existingPLIRating, existingProjectRating };
        } catch (error) {
            return { existingPLIRating: null, existingProjectRating: null };
        }
    }

    /**
     * @desc Get sprints for a project in a specific month
     * @param {String} project The project name
     * @param {Number} month The month number
     * @param {Number} year The year number
     * @param {String} username The username
     * @return {Array} Array of sprints
     */
    static async getProjectSprints (project, month, year, username) {
        try {
            // Get RAG data for the project in the specified month
            return await RagModel.find({
                project,
                sprintEnd: {
                    $gte: new Date(parseInt(year), parseInt(month) - 1, 1),
                    $lte: new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999)
                },
                'team.member': username
            }).lean();
        } catch (error) {
            return [];
        }
    }

    /**
     * @desc Get user details including role and mentor ID
     * @param {String} username The username
     * @param {String} existingMenteeId Existing mentee ID if available
     * @return {Object} User details
     */
    static async getUserDetails (username, existingMenteeId) {
        try {
            const user = await UserModel.findOne({ label: { $in: [username] } }).lean();
            if (!user) {
                return {
                    role: 'Developers',
                    menteeId: existingMenteeId || null,
                    mentorId: null
                };
            }

            return {
                role: user.designation ? this.mapDesignationToRole(user.designation) : 'Developers',
                menteeId: user._id,
                mentorId: user.mentorId || null
            };
        } catch (error) {
            return {
                role: 'Developers',
                menteeId: existingMenteeId || null,
                mentorId: null
            };
        }
    }

    /**
     * @desc Get PLI parameters for a role and project type
     * @param {String} role The user role
     * @param {String} projectType The project type
     * @return {Object} PLI parameters
     */
    static async getPLIParameters (role, projectType) {
        try {
            // Get the single PLI parameters document
            const pliParametersDoc = await PLIParametersModel.findOne({ isActive: 1 }).lean();

            // Find the role-specific parameters for this user's role
            let roleParameters = null;
            if (pliParametersDoc && pliParametersDoc.roleParameters) {
                roleParameters = pliParametersDoc.roleParameters.find(rp =>
                    rp.applicableRole === role
                );
            }

            // Find the parameter set for this project type
            let pliParameter = null;
            if (roleParameters && roleParameters.parameters) {
                pliParameter = roleParameters.parameters.find(p =>
                    p.projectType === (projectType || 'Dedicated')
                );
            }

            return {
                pliParametersDoc,
                pliParameter
            };
        } catch (error) {
            return {
                pliParametersDoc: null,
                pliParameter: null
            };
        }
    }

    /**
     * @desc Get sprint metrics for a project and username
     * @param {String} project The project name
     * @param {String} username The username
     * @return {Object} Sprint metrics map
     */
    static async getSprintMetrics (project, username) {
        try {
            // Fetch sprint metrics data for this user where repo is 'Overall'
            const sprintMetrics = await SprintMetricsModel.find({
                projectName: project,
                resource: `${username.split('.')[0]}-growexxer`,
                repo: 'Overall'
            }).lean();

            // Map sprint metrics by sprint number
            const sprintMetricsMap = {};
            sprintMetrics.forEach(metric => {
                if (metric.sprintNumber) {
                    sprintMetricsMap[metric.sprintNumber] = metric;
                }
            });

            return sprintMetricsMap;
        } catch (error) {
            return {};
        }
    }

    /**
     * @desc Calculate score for a parameter based on its name
     * @param {String} paramName The parameter name
     * @param {Object} sprint The sprint data
     * @param {Object} memberData The member data
     * @param {Object} sprintMetricsMap The sprint metrics map
     * @return {Number} The calculated score
     */
    static calculateParameterScore (paramName, sprint, memberData, sprintMetricsMap) {
        // Declare variables outside switch to avoid lexical declaration issues
        let ragColor;
        let sprintAvgRating;
        let sonarRating;

        switch (paramName) {
            // RAG parameters
            case 'rag':
            case 'RAG':
            case 'RAG (Budget + Open/Close + B2D + Escalations)':
                ragColor = RagService.getOverallRag({
                    ...sprint,
                    memberData,
                    memberClientEscalation: memberData.escalationCount || 0
                });
                return this.ragColorToScore(ragColor);

            // Sprint Average Rating parameters
            case 'sprintAverageRating':
            case 'Sprint Average RATING (PR) + (SONAR)':
                const sprintMetricsData = sprintMetricsMap[sprint.sprintNumber] || {};
                sprintAvgRating = sprintMetricsData.sprintAverage || 0;
                sonarRating = sprintMetricsData.sonarRating || 0;
                return Number(sprintAvgRating) + Number(sonarRating);

            // B2D Score parameter
            case 'b2dScore':
            case 'B2D (NONE then best)':
            case 'B2D Reported (more the better)':
                return memberData.b2dCount || 0;

            // Tech Audit Rating parameter
            case 'techAuditRating':
            case 'Tech Audit/PR Audit':
            case 'Tech Audit':
                return sprint.techAudit ? parseFloat(sprint.techAudit) || 0 : 0;

            // Effort Variance Rating parameter
            case 'effortVarianceRating':
            case 'Velocity / Effort Variance':
                return memberData.effortVariance || 0;

            // Customer Feedback / Escalation parameters
            case 'Customer Feedback / Escalation - (Individual)':
            case 'Customer Feedback / Escalation':
                return memberData.escalationCount ? -memberData.escalationCount : 0;

            // Unplanned Leave parameters
            case 'Least Leave (Un-Planned) - (Individual) - Billing Loss':
            case 'Least Leave (Un-Planned)':
                return memberData.unplannedLeaves ? -memberData.unplannedLeaves : 0;

            // Self-managing project parameter
            case 'Self managing the project, proactiveness - 1/0 ( Project Specific) - Individual':
                return memberData.selfManaging || 0;

            // B2D from Customer parameter
            case 'B2D from Customer (NONE then best)':
                return memberData.customerB2D || 0;

            // B2D Team parameter
            case 'B2D (Team wide less the better)':
                return sprint.teamB2D ? -sprint.teamB2D : 0;

            // Billing Sheet parameter
            case 'Accurate timely Billing Sheet':
                return memberData.billingAccuracy || 0;

            // Process Audit parameter
            case 'Process Audit':
                return sprint.processAudit ? parseFloat(sprint.processAudit) || 0 : 0;

            default:
                return sprint[paramName] || memberData[paramName] || 0;
        }
    }

    /**
     * @desc Process sprint score for a parameter
     * @param {Object} sprint The sprint data
     * @param {String} paramName The parameter name
     * @param {Object} pliParam The PLI parameter object
     * @param {Object} existingParameterScores Existing parameter scores if available
     * @param {String} username The username
     * @param {Object} sprintMetricsMap Sprint metrics map
     */
    static processSprintScore (sprint, paramName, pliParam, existingParameterScores, username, sprintMetricsMap) {
        let existingSprintScore = null;
        if (existingParameterScores && existingParameterScores.childScores) {
            const childScore = existingParameterScores.childScores.find(cs =>
                cs.childParameterId === paramName
            );

            if (childScore && childScore.sprintScores) {
                // Find the sprint score for this sprint number
                existingSprintScore = childScore.sprintScores.find(ss =>
                    ss.sprintNumber === sprint.sprintNumber
                );
            }
        }

        // If we have an existing sprint score, use it
        if (existingSprintScore && existingSprintScore.score !== undefined) {
            pliParam.pliParameter.sprintwiseScores.push({
                sprintname: sprint.sprintNumber,
                score: existingSprintScore.score,
                comment: existingSprintScore.comment || '',
                fromPLIRating: true
            });
            return;
        }

        // If no existing score, calculate it using the original method
        // Find the team member data for this employee
        const memberData = (sprint.team && sprint.team.find((m) => m.member === username)) || {};

        // Get the score based on parameter name
        const score = this.calculateParameterScore(paramName, sprint, memberData, sprintMetricsMap);

        pliParam.pliParameter.sprintwiseScores.push({
            sprintname: sprint.sprintNumber,
            score: score
        });
    }

    /**
     * @desc Process PLI parameters and calculate scores
     * @param {Object} pliParameter The PLI parameter
     * @param {Array} sprints The sprints data
     * @param {String} username The username
     * @param {Object} existingProjectRating Existing project rating if available
     * @param {Object} sprintMetricsMap Sprint metrics map
     * @return {Array} Processed PLI parameters
     */
    static processParameters (pliParameter, sprints, username, existingProjectRating, sprintMetricsMap) {
        const result = [];

        if (!pliParameter || !pliParameter.childParameters) {
            return result;
        }

        pliParameter.childParameters.forEach(param => {
            const pliParam = {
                pliParameter: {
                    name: param.name,
                    sprintwiseScores: [],
                    weightage: param.weightage || 0
                }
            };

            // Check if we have existing PLI Rating data for this parameter
            let existingParameterScores = null;
            if (existingProjectRating && existingProjectRating.parameterScores) {
                existingParameterScores = existingProjectRating.parameterScores.find(ps => {
                    return ps.childScores && ps.childScores.some(cs => cs.childParameterId === param.name);
                });
            }

            // Process each sprint to get scores for this parameter
            sprints.forEach(sprint => {
                this.processSprintScore(sprint, param.name, pliParam, existingParameterScores, username, sprintMetricsMap);
            });

            // Add this parameter to the result
            result.push(pliParam);
        });

        return result;
    }

    /**
     * @desc Enhance data with additional fields needed for frontend
     * @param {Array} data The data array
     * @return {Array} Enhanced data
     */
    static enhanceData (data) {
        if (!data || !data.length) {
            return data;
        }

        data.forEach(item => {
            // Add childParameterId directly to each parameter
            item.childParameterId = item.pliParameter.name;

            // Calculate weightage and calculation fields
            const scores = item.pliParameter.sprintwiseScores
                .map(sprint => sprint.score)
                .filter(score => score !== undefined && score !== null);

            let averageScore = 0;
            if (scores.length > 0) {
                const totalScore = scores.reduce(
                    (sum, score) => sum + Number(score),
                    0
                );
                averageScore = totalScore / scores.length;
            }

            const calculatedAverage = Number(averageScore.toFixed(2));
            const weightage = item.pliParameter.weightage || 0;
            const weightageAverage = (calculatedAverage * weightage / 100).toFixed(2);

            // Add calculation and weightageAverage to the parameter
            item.pliParameter.calculation = calculatedAverage;
            item.pliParameter.weightageAverage = weightageAverage;

            // Convert sprintwiseScores to the format needed for POST API
            if (item.pliParameter.sprintwiseScores && item.pliParameter.sprintwiseScores.length > 0) {
                item.sprintScores = item.pliParameter.sprintwiseScores.map(sprintScore => ({
                    sprintNumber: sprintScore.sprintname,
                    score: sprintScore.score,
                    comment: ''
                }));
            }
        });

        return data;
    }

    /**
     * @desc Prepare result object with additional metadata
     * @param {Object} baseResult Base result object
     * @param {String} projectId Project ID
     * @param {Object} pliParametersDoc PLI parameters document
     * @param {String|Number} weightage Project weightage
     * @return {Object} Enhanced result object
     */
    static prepareResultObject (baseResult, projectId, pliParametersDoc, weightage) {
        const result = { ...baseResult };

        // Add project ID and project weightage if available
        if (projectId) {
            result.projectId = projectId;
            result.projectWeightage = weightage ? parseInt(weightage) : 100;
        }

        // Add parameter ID if available
        if (pliParametersDoc && pliParametersDoc._id) {
            result.parameterId = pliParametersDoc._id;
        }

        return result;
    }

    /**
     * @desc This function fetches project sprint data based on the provided parameters
     * <AUTHOR>
     * @since 08/21/2024
     * @param {Object} req Request object
     */
    static async getProjectSprintData (req) {
        const { project, month, year, employeeName, projectType, weightage } = req.query;

        if (!employeeName) {
            return {
                success: false,
                error: 'Employee name is required'
            };
        }

        try {
            // Step 1: Get user and project information
            const { menteeId: initialMenteeId } = await this.findUserByUsername(employeeName);
            const { projectId } = await this.findProjectByName(project);

            // Step 2: Find existing PLI rating if any
            const { existingProjectRating } =
                await this.findExistingPLIRating(initialMenteeId, month, year, projectId);

            // Step 3: Get sprints for the project in the specified month
            const sprints = await this.getProjectSprints(project, month, year, employeeName);

            // If no sprints found for the month
            if (!sprints || sprints.length === 0) {
                return {
                    success: true,
                    data: []
                };
            }

            // Step 4: Get user details including role and mentor ID
            const { role, menteeId, mentorId } = await this.getUserDetails(employeeName, initialMenteeId);

            // Step 5: Get PLI parameters for the user's role and project type
            const { pliParametersDoc, pliParameter } = await this.getPLIParameters(role, projectType);

            // Step 6: Get sprint metrics for the project and user
            const sprintMetricsMap = await this.getSprintMetrics(project, employeeName);

            // Step 7: Process parameters and calculate scores
            const parameterData = this.processParameters(
                pliParameter,
                sprints,
                employeeName,
                existingProjectRating,
                sprintMetricsMap
            );

            // Step 8: Create the base result object
            const baseResult = {
                data: parameterData,
                menteeId,
                mentorId,
                month: parseInt(month),
                year: parseInt(year),
                comments: ''
            };

            // Step 9: Prepare final result with additional metadata
            const result = this.prepareResultObject(baseResult, projectId, pliParametersDoc, weightage);

            // Step 10: Enhance data with additional fields needed for frontend
            if (result.data && result.data.length > 0) {
                this.enhanceData(result.data);
            }

            return {
                success: true,
                ...result
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'An error occurred while fetching project sprint data'
            };
        }
    }

    /**
   * Convert RAG color to numeric score
   * @param {string} color The RAG color: 'green', 'amber', or 'red'
   * @return {number} The numeric score: Green=1, Amber=0, Red=-1
   */
    static ragColorToScore (color) {
        switch (color) {
            case 'green':
                return 1;
            case 'amber':
                return 0;
            case 'red':
                return -1;
            default:
                return 0;
        }
    }
}

module.exports = ProjectSprintDataService;
