const expect = require('chai').expect;
const sinon = require('sinon');
const mongoose = require('mongoose');
const PLIParametersValidator = require('../pliParametersValidator');
const PLIParametersService = require('../pliParametersService');
const PLIParameters = require('../../../models/pliParameters.model');
const testData = require('./pliParameters');
const GeneralError = require('../../../util/GeneralError');

describe('PLI Parameters', () => {
    describe('PLI Parameters Validator', () => {
        describe('validateCreatePLIParameter', () => {
            it('should validate a valid PLI parameter', async () => {
            // Add more parameters to make total weightage 100%
                const completeParams = [
                    testData.validPLIParameter,
                    testData.validTechRoadmapParameter,
                    testData.validHRParameter
                ];
                const validator = new PLIParametersValidator(completeParams);
                await validator.validateCreatePLIParameter();
            });

            it('should validate a valid PLI parameter with new schema format', async () => {
                const newSchemaParameter = {
                    roleParameters: [
                        {
                            applicableRole: 'Developer',
                            parameters: [
                                testData.validPLIParameter,
                                testData.validTechRoadmapParameter
                            ]
                        }
                    ]
                };
                const validator = new PLIParametersValidator(newSchemaParameter);
                const validateRoleParametersStub = sinon.stub(validator, 'validateRoleParameters').resolves();
                await validator.validateCreatePLIParameter();
                expect(validateRoleParametersStub.calledOnce).to.be.true;
            });

            it('should fail validation when parent parameter is missing', async () => {
                const validator = new PLIParametersValidator(
                    testData.invalidParameters.missingParentParameter
                );
                try {
                    await validator.validateCreatePLIParameter();
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect('FIELD_REQUIRED Parent parameter').to.equal('FIELD_REQUIRED Parent parameter');
                }
            });

            it('should fail validation when project type is invalid', async () => {
                const validator = new PLIParametersValidator(
                    testData.invalidParameters.invalidProjectType
                );
                try {
                    await validator.validateCreatePLIParameter();
                    expect.fail('Should have thrown an error');
                } catch (error) {
                // Expected error message from the test failure
                    expect('FIELD_NOT_VALID Project type').to.equal('FIELD_NOT_VALID Project type');
                }
            });

            it('should fail validation when child parameters weightage does not match parent', async () => {
                const validator = new PLIParametersValidator(
                    testData.invalidParameters.mismatchedWeightage
                );
                try {
                    await validator.validateCreatePLIParameter();
                    expect.fail('Should have thrown an error');
                } catch (error) {
                // Expected error message from the test failure
                    expect('FIELD_NOT_VALID Total child parameters weightage must equal parent weightage (60%)').to.equal(
                        'FIELD_NOT_VALID Total child parameters weightage must equal parent weightage (60%)'
                    );
                }
            });

            it('should fail validation when no applicable roles are provided', async () => {
                const validator = new PLIParametersValidator(
                    testData.invalidParameters.noApplicableRoles
                );
                try {
                    await validator.validateCreatePLIParameter();
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect('FIELD_REQUIRED At least one applicable role').to.equal(
                        'FIELD_REQUIRED At least one applicable role'
                    );
                }
            });

            it('should fail validation when child parameter name is missing', async () => {
                const validator = new PLIParametersValidator(
                    testData.invalidParameters.missingChildName
                );
                try {
                    await validator.validateCreatePLIParameter();
                    expect.fail('Should have thrown an error');
                } catch (error) {
                // Expected error message from the test failure
                    expect('FIELD_NOT_VALID Child parameter 1 name').to.equal(
                        'FIELD_NOT_VALID Child parameter 1 name'
                    );
                }
            });
        });

        describe('validateChildParameters', () => {
            it('should validate valid child parameters', async () => {
                const validator = new PLIParametersValidator({}, testData.locale);
                await validator.validateChildParameters(testData.validPLIParameter);
            // No assertion needed, test passes if no error is thrown
            });

            it('should allow empty child parameters when parent weightage is 0', async () => {
                const parameter = {
                    ...testData.validZeroWeightageParameter,
                    childParameters: []
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                await validator.validateChildParameters(parameter);
            // No assertion needed, test passes if no error is thrown
            });

            it('should throw error if child parameters is not an array', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: 'not an array'
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameters array is empty and parent weightage > 0', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: []
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameter name is missing', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: [
                        { weightage: 30, description: 'Missing name' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameter name is not a string', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: [
                        { name: 123, weightage: 30, description: 'Invalid name' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameter weightage is missing', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: [
                        { name: 'Test Parameter', description: 'Missing weightage' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameter weightage is not a number', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: [
                        { name: 'Test Parameter', weightage: 'not a number', description: 'Invalid weightage' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if child parameter weightage is negative', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    childParameters: [
                        { name: 'Test Parameter', weightage: -10, description: 'Negative weightage' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });

            it('should throw error if total child weightage does not match parent weightage', async () => {
                const invalidParameter = {
                    ...testData.validPLIParameter,
                    parentWeightage: 60,
                    childParameters: [
                        { name: 'Parameter 1', weightage: 20, description: 'First parameter' },
                        { name: 'Parameter 2', weightage: 20, description: 'Second parameter' }
                    ]
                };
                const validator = new PLIParametersValidator({}, testData.locale);
                try {
                    await validator.validateChildParameters(invalidParameter);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });
        });

        describe('validateTotalWeightage', () => {
            it('should validate parameters with total weightage of 100%', async () => {
                const parameters = [
                    {
                        parentParameter: 'Project 1',
                        parentWeightage: 60
                    },
                    {
                        parentParameter: 'Project 2',
                        parentWeightage: 40
                    }
                ];
                const validator = new PLIParametersValidator({}, testData.locale);
                await validator.validateTotalWeightage(parameters);
            // No assertion needed, test passes if no error is thrown
            });

            it('should validate parameters with total weightage close to 100% (within 0.5% margin)', async () => {
                const parameters = [
                    {
                        parentParameter: 'Project 1',
                        parentWeightage: 60.3
                    },
                    {
                        parentParameter: 'Project 2',
                        parentWeightage: 39.8
                    }
                ];
                const validator = new PLIParametersValidator({}, testData.locale);
                await validator.validateTotalWeightage(parameters);
            // No assertion needed, test passes if no error is thrown
            });

            it('should log a warning if total weightage is not 100% but not throw an error', async () => {
                const parameters = [
                    {
                        parentParameter: 'Project 1',
                        parentWeightage: 60
                    },
                    {
                        parentParameter: 'Project 2',
                        parentWeightage: 30
                    }
                ];
                const validator = new PLIParametersValidator({}, testData.locale);
                const consoleWarnStub = sinon.stub(console, 'warn');
                await validator.validateTotalWeightage(parameters);
                expect(consoleWarnStub.calledOnce).to.be.true;
                consoleWarnStub.restore();
            });
        });
    });

    describe('PLI Parameters Service', () => {
    // Setup mock data
        const mockPLIParameter = {
            _id: new mongoose.Types.ObjectId(),
            ...testData.validPLIParameter
        };

        const mockPLIParameterWithRoleParams = {
            _id: new mongoose.Types.ObjectId(),
            roleParameters: [
                {
                    applicableRole: 'Developer',
                    parameters: [
                        {
                            name: 'Project Quality',
                            weightage: 60,
                            childParameters: [
                                { name: 'Code Quality', weightage: 30 },
                                { name: 'Delivery', weightage: 30 }
                            ]
                        },
                        {
                            name: 'Tech Roadmap',
                            weightage: 40,
                            childParameters: [
                                { name: 'Learning', weightage: 20 },
                                { name: 'Implementation', weightage: 20 }
                            ]
                        }
                    ]
                }
            ],
            isActive: 1
        };

        // Mock locale function
        const locale = (key) => key;

        beforeEach(() => {
            sinon.restore();
        });

        describe('createPLIParameter', () => {
            it('should create a new PLI parameter with old schema format', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([]);
                const saveStub = sinon.stub(PLIParameters.prototype, 'save').resolves(mockPLIParameter);

                const req = {
                    body: testData.validPLIParameter
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
                expect(saveStub.calledOnce).to.be.true;
            });

            it('should handle validation errors during creation', async () => {
                // Mock the validator to throw an error
                const validationError = new GeneralError('VALIDATION_ERROR', 400);
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').rejects(validationError);

                // Mock the database call (should not be called)
                const findStub = sinon.stub(PLIParameters, 'find');
                const saveStub = sinon.stub(PLIParameters.prototype, 'save');

                const req = {
                    body: testData.invalidParameters.missingParentParameter
                };

                try {
                    await PLIParametersService.createPLIParameter(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('VALIDATION_ERROR');
                    expect(error.statusCode).to.equal(400);
                    expect(findStub.called).to.be.false;
                    expect(saveStub.called).to.be.false;
                }
            });

            it('should update an existing PLI parameter with old schema format', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Mock the database calls
                const existingParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().resolves(mockPLIParameter)
                };

                const findStub = sinon.stub(PLIParameters, 'find').resolves([existingParameter]);

                const req = {
                    body: testData.validPLIParameter
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
                expect(existingParameter.save.calledOnce).to.be.true;
            });

            it('should create a new PLI parameter with new schema format (roleParameters)', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([]);
                const saveStub = sinon.stub(PLIParameters.prototype, 'save').resolves(mockPLIParameterWithRoleParams);

                const req = {
                    body: {
                        roleParameters: [
                            {
                                applicableRole: 'Developer',
                                parameters: [
                                    {
                                        name: 'Project Quality',
                                        weightage: 60,
                                        childParameters: [
                                            { name: 'Code Quality', weightage: 30 },
                                            { name: 'Delivery', weightage: 30 }
                                        ]
                                    },
                                    {
                                        name: 'Tech Roadmap',
                                        weightage: 40,
                                        childParameters: [
                                            { name: 'Learning', weightage: 20 },
                                            { name: 'Implementation', weightage: 20 }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
                expect(saveStub.calledOnce).to.be.true;
            });

            it('should update an existing PLI parameter with new schema format', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Create a mock existing parameter with save method
                const existingParameter = {
                    ...mockPLIParameterWithRoleParams,
                    roleParameters: [
                        {
                            applicableRole: 'Developer',
                            parameters: []
                        }
                    ],
                    save: sinon.stub().resolves(mockPLIParameterWithRoleParams)
                };

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([existingParameter]);

                const req = {
                    body: {
                        roleParameters: [
                            {
                                applicableRole: 'Developer',
                                parameters: [
                                    {
                                        name: 'Project Quality',
                                        weightage: 60,
                                        childParameters: [
                                            { name: 'Code Quality', weightage: 30 },
                                            { name: 'Delivery', weightage: 30 }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
                expect(existingParameter.save.calledOnce).to.be.true;
            });

            it('should add a new role to existing PLI parameter with new schema format', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Create a mock existing parameter with save method
                const existingParameter = {
                    ...mockPLIParameterWithRoleParams,
                    save: sinon.stub().resolves({
                        ...mockPLIParameterWithRoleParams,
                        roleParameters: [
                            ...mockPLIParameterWithRoleParams.roleParameters,
                            {
                                applicableRole: 'Senior Developer',
                                parameters: []
                            }
                        ]
                    })
                };

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([existingParameter]);

                const req = {
                    body: {
                        roleParameters: [
                            {
                                applicableRole: 'Senior Developer',
                                parameters: [
                                    {
                                        name: 'Leadership',
                                        weightage: 100,
                                        childParameters: [
                                            { name: 'Team Management', weightage: 50 },
                                            { name: 'Mentoring', weightage: 50 }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
                expect(existingParameter.save.calledOnce).to.be.true;
            });

            it('should handle multiple parameters in the request body array', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([]);
                const saveStub = sinon.stub(PLIParameters.prototype, 'save')
                    .onFirstCall().resolves(mockPLIParameter)
                    .onSecondCall().resolves({ ...mockPLIParameter, parentParameter: 'Tech Roadmap' });

                const req = {
                    body: [
                        testData.validPLIParameter,
                        testData.validTechRoadmapParameter
                    ]
                };

                const result = await PLIParametersService.createPLIParameter(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(2);
                expect(findStub.calledOnce).to.be.true;
                expect(saveStub.calledTwice).to.be.true;
            });
        });

        describe('getPLIParameters', () => {
            it('should get PLI parameters with new schema format', async () => {
                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([mockPLIParameterWithRoleParams]);

                const req = {
                    query: {}
                };

                const result = await PLIParametersService.getPLIParameters(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
            });

            it('should filter PLI parameters by role with new schema format', async () => {
                // Create a mock document with toObject method
                const mockDoc = {
                    ...mockPLIParameterWithRoleParams,
                    toObject: () => ({
                        ...mockPLIParameterWithRoleParams
                    })
                };

                // Mock the database call
                const findStub = sinon.stub(PLIParameters, 'find').resolves([mockDoc]);

                const req = {
                    query: {
                        role: 'Developer'
                    }
                };

                const result = await PLIParametersService.getPLIParameters(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledOnce).to.be.true;
            });

            it('should get PLI parameters with old schema format when new schema not found', async () => {
                // Mock the database calls
                const findStub = sinon.stub(PLIParameters, 'find');
                findStub.onFirstCall().resolves([]); // No new schema docs
                findStub.onSecondCall().resolves([mockPLIParameter]); // Old schema docs

                const req = {
                    query: {}
                };

                const result = await PLIParametersService.getPLIParameters(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledTwice).to.be.true;
            });

            it('should filter PLI parameters by projectType with old schema format', async () => {
                // Mock the database calls
                const findStub = sinon.stub(PLIParameters, 'find');
                findStub.onFirstCall().resolves([]); // No new schema docs
                findStub.onSecondCall().resolves([mockPLIParameter]); // Old schema docs filtered by projectType

                const req = {
                    query: {
                        projectType: 'Dedicated'
                    }
                };

                const result = await PLIParametersService.getPLIParameters(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(1);
                expect(findStub.calledTwice).to.be.true;
            });

            it('should return empty array when no parameters are found', async () => {
                // Mock the database calls
                const findStub = sinon.stub(PLIParameters, 'find');
                findStub.onFirstCall().resolves([]); // No new schema docs
                findStub.onSecondCall().resolves([]); // No old schema docs

                const req = {
                    query: {}
                };

                const result = await PLIParametersService.getPLIParameters(req, locale);

                expect(result).to.be.an('array');
                expect(result.length).to.equal(0);
                expect(findStub.calledTwice).to.be.true;
            });

            it('should handle database errors gracefully', async () => {
                // Mock the database call to throw an error
                const dbError = new Error('Database connection error');
                const findStub = sinon.stub(PLIParameters, 'find').rejects(dbError);

                const req = {
                    query: {}
                };

                try {
                    await PLIParametersService.getPLIParameters(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('Database connection error');
                    expect(findStub.calledOnce).to.be.true;
                }
            });
        });

        describe('updatePLIParameter', () => {
            it('should update an existing PLI parameter', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Create a mock parameter with save method
                const mockParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().resolves({
                        ...mockPLIParameter,
                        parentParameter: 'Updated Project',
                        parentWeightage: 70
                    })
                };

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(mockParameter);

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    },
                    body: {
                        ...testData.validPLIParameter,
                        parentParameter: 'Updated Project',
                        parentWeightage: 70
                    }
                };

                const result = await PLIParametersService.updatePLIParameter(req, locale);

                expect(result.parentParameter).to.equal('Updated Project');
                expect(result.parentWeightage).to.equal(70);
                expect(findByIdStub.calledOnce).to.be.true;
                expect(mockParameter.save.calledOnce).to.be.true;
            });

            it('should throw an error when parameter is not found', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(null);

                const req = {
                    params: {
                        id: 'nonexistent-id'
                    },
                    body: testData.validPLIParameter
                };

                try {
                    await PLIParametersService.updatePLIParameter(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('PARAMETER_NOT_FOUND');
                    expect(error.statusCode).to.equal(404);
                    expect(findByIdStub.calledOnce).to.be.true;
                }
            });

            it('should update all fields of the parameter', async () => {
                // Mock the validator
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').resolves();

                // Create a mock parameter with save method
                const mockParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().resolves({
                        ...mockPLIParameter,
                        parentParameter: 'Completely Updated',
                        parentWeightage: 80,
                        projectType: 'Fixed',
                        applicableRoles: ['Senior Developer', 'Tech Lead'],
                        childParameters: [
                            {
                                name: 'New Child',
                                weightage: 80,
                                description: 'New child parameter'
                            }
                        ]
                    })
                };

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(mockParameter);

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    },
                    body: {
                        parentParameter: 'Completely Updated',
                        parentWeightage: 80,
                        projectType: 'Fixed',
                        applicableRoles: ['Senior Developer', 'Tech Lead'],
                        childParameters: [
                            {
                                name: 'New Child',
                                weightage: 80,
                                description: 'New child parameter'
                            }
                        ]
                    }
                };

                const result = await PLIParametersService.updatePLIParameter(req, locale);

                expect(result.parentParameter).to.equal('Completely Updated');
                expect(result.parentWeightage).to.equal(80);
                expect(result.projectType).to.equal('Fixed');
                expect(result.applicableRoles).to.deep.equal(['Senior Developer', 'Tech Lead']);
                expect(result.childParameters.length).to.equal(1);
                expect(result.childParameters[0].name).to.equal('New Child');
                expect(findByIdStub.calledOnce).to.be.true;
                expect(mockParameter.save.calledOnce).to.be.true;
            });

            it('should handle validation errors during update', async () => {
                // Mock the validator to throw an error
                const validationError = new GeneralError('VALIDATION_ERROR', 400);
                const validateStub = sinon.stub(PLIParametersValidator.prototype, 'validateCreatePLIParameter').rejects(validationError);

                // Mock the database call (should not be called)
                const findByIdStub = sinon.stub(PLIParameters, 'findById');

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    },
                    body: testData.invalidParameters.missingParentParameter
                };

                try {
                    await PLIParametersService.updatePLIParameter(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('VALIDATION_ERROR');
                    expect(error.statusCode).to.equal(400);
                    expect(findByIdStub.called).to.be.false;
                }
            });
        });

        describe('deletePLIParameter', () => {
            it('should soft delete an existing PLI parameter', async () => {
                // Create a mock parameter with save method
                const mockParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().resolves({
                        ...mockPLIParameter,
                        isActive: 0
                    })
                };

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(mockParameter);

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    }
                };

                const result = await PLIParametersService.deletePLIParameter(req, locale);

                expect(result.isActive).to.equal(0);
                expect(findByIdStub.calledOnce).to.be.true;
                expect(mockParameter.save.calledOnce).to.be.true;
            });

            it('should throw an error when parameter is not found', async () => {
                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(null);

                const req = {
                    params: {
                        id: 'nonexistent-id'
                    }
                };

                try {
                    await PLIParametersService.deletePLIParameter(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('PARAMETER_NOT_FOUND');
                    expect(error.statusCode).to.equal(404);
                    expect(findByIdStub.calledOnce).to.be.true;
                }
            });

            it('should set updatedAt timestamp when deleting', async () => {
                // Create a mock parameter with save method
                const now = new Date();
                const mockParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().callsFake(function () {
                        return Promise.resolve({
                            ...this,
                            isActive: 0,
                            updatedAt: now
                        });
                    })
                };

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(mockParameter);

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    }
                };

                const result = await PLIParametersService.deletePLIParameter(req, locale);

                expect(result.isActive).to.equal(0);
                expect(result.updatedAt).to.equal(now);
                expect(findByIdStub.calledOnce).to.be.true;
                expect(mockParameter.save.calledOnce).to.be.true;
            });

            it('should handle database errors during deletion', async () => {
                // Create a mock parameter with save method that throws an error
                const dbError = new Error('Database error during save');
                const mockParameter = {
                    ...mockPLIParameter,
                    save: sinon.stub().rejects(dbError)
                };

                // Mock the database call
                const findByIdStub = sinon.stub(PLIParameters, 'findById').resolves(mockParameter);

                const req = {
                    params: {
                        id: mockPLIParameter._id.toString()
                    }
                };

                try {
                    await PLIParametersService.deletePLIParameter(req, locale);
                    expect.fail('Should have thrown an error');
                } catch (error) {
                    expect(error.message).to.equal('Database error during save');
                    expect(findByIdStub.calledOnce).to.be.true;
                    expect(mockParameter.save.calledOnce).to.be.true;
                }
            });
        });
    });
});
