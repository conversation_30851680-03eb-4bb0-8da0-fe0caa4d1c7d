const GetPliRatingByIdService = require('./getPliRatingByIdService');
const GetPliRatingByIdValidator = require('./getPliRatingByIdValidator');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for getting PLI Rating by ID
 */
class GetPliRatingByIdController {
    /**
     * @desc This function is being used to get PLI rating by ID
     * <AUTHOR>
     * @since 16/05/2025
     * @param {Object} req Request
     * @param {Object} req.query RequestQuery
     * @param {string} req.query.pliRatingId PLI Rating ID
     * @param {function} res Response
     */
    static async getPliRatingById (req, res) {
        try {
            // Validate request parameters
            const validator = new GetPliRatingByIdValidator(req.query, res.__);
            validator.validateGetPliRatingById();

            // Call service
            const data = await GetPliRatingByIdService.getPliRatingById(req.query, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = GetPliRatingByIdController;
