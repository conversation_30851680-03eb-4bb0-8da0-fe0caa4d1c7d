const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/api/pli-rating/by-id'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: ['PLI Rating'],
            description: 'Get PLI rating by ID',
            summary: 'Get PLI rating by ID with populated names',
            parameters: [
                {
                    in: 'query',
                    name: 'pliRatingId',
                    description: 'PLI Rating ID',
                    required: true,
                    type: 'string'
                }
            ],
            responses: {
                200: {
                    description: 'PLI rating fetched successfully.',
                    schema: {
                        $ref: '#/definitions/successGetPliRatingById'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                404: {
                    description: 'PLI Rating not found',
                    schema: {
                        $ref: '#/definitions/notFoundError'
                    }
                }
            }
        }
    };

    // Define response schema for getPliRatingById
    swaggerJson.definitions.successGetPliRatingById = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                properties: {
                    _id: {
                        type: 'string',
                        example: '507f1f77bcf86cd799439011'
                    },
                    mentee: {
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                example: 'John Doe'
                            },
                            email: {
                                type: 'string',
                                example: '<EMAIL>'
                            },
                            employeeId: {
                                type: 'number',
                                example: 12345
                            }
                        }
                    },
                    mentor: {
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                example: 'Jane Smith'
                            },
                            email: {
                                type: 'string',
                                example: '<EMAIL>'
                            },
                            employeeId: {
                                type: 'number',
                                example: 12346
                            }
                        }
                    },
                    month: {
                        type: 'number',
                        example: 5
                    },
                    year: {
                        type: 'number',
                        example: 2025
                    },
                    projectRatings: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                projectId: {
                                    type: 'string',
                                    example: '507f1f77bcf86cd799439011'
                                },
                                project: {
                                    type: 'object',
                                    properties: {
                                        name: {
                                            type: 'string',
                                            example: 'Project Alpha'
                                        },
                                        code: {
                                            type: 'string',
                                            example: 'ALPHA-001'
                                        },
                                        client: {
                                            type: 'string',
                                            example: 'Client XYZ'
                                        }
                                    }
                                },
                                projectWeightage: {
                                    type: 'number',
                                    example: 40
                                },
                                parameterScores: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            parameter: {
                                                type: 'object',
                                                properties: {
                                                    name: {
                                                        type: 'string',
                                                        example: 'Code Quality'
                                                    },
                                                    type: {
                                                        type: 'string',
                                                        example: 'Dedicated',
                                                        enum: ['Dedicated', 'Fixed']
                                                    }
                                                }
                                            },
                                            autoFilled: {
                                                type: 'boolean',
                                                example: true
                                            },
                                            score: {
                                                type: 'number',
                                                example: 4
                                            },
                                            comments: {
                                                type: 'string',
                                                example: 'Good performance in code quality'
                                            },
                                            childScores: {
                                                type: 'array',
                                                items: {
                                                    type: 'object',
                                                    properties: {
                                                        childParameter: {
                                                            type: 'string',
                                                            example: 'RAG (Budget + Open/Close + B2D + Escalations)'
                                                        },
                                                        childParameterWeightage: {
                                                            type: 'number',
                                                            example: 25,
                                                            description: 'Weightage of the child parameter based on role and project type'
                                                        },
                                                        calculation: {
                                                            type: 'number',
                                                            example: 4.2,
                                                            description: 'Average of all sprint scores for this child parameter'
                                                        },
                                                        weightageAverage: {
                                                            type: 'number',
                                                            example: 1.05,
                                                            description: 'Weighted contribution (calculation * childParameterWeightage / 100)'
                                                        },
                                                        sprintScores: {
                                                            type: 'array',
                                                            items: {
                                                                type: 'object',
                                                                properties: {
                                                                    sprintNumber: {
                                                                        type: 'string',
                                                                        example: 'WEB Sprint 6'
                                                                    },
                                                                    score: {
                                                                        type: 'number',
                                                                        example: -1
                                                                    },
                                                                    comment: {
                                                                        type: 'string',
                                                                        example: ''
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    status: {
                        type: 'string',
                        example: 'Draft'
                    },
                    finalScore: {
                        type: 'number',
                        example: 0
                    },
                    superAdminOverride: {
                        type: 'boolean',
                        example: false
                    },
                    isFrozen: {
                        type: 'boolean',
                        example: false
                    },
                    createdAt: {
                        type: 'string',
                        example: '2025-05-15T22:29:14.667Z'
                    },
                    updatedAt: {
                        type: 'string',
                        example: '2025-05-15T22:29:14.681Z'
                    }
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    return swaggerJson;
};
