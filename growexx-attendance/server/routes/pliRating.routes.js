// server/routes/pliRating.routes.js
const express = require('express');
const router = express.Router();
const pliController = require('../services/getProjectsBasedMonth/getProjectController');
const {
    getMenteeProjectsValidator,
    getMenteeProjectsByLabelValidator,
    syncProjectsValidator
} = require('../services/getProjectsBasedMonth/getProjectsValidator');
const UpdateSprintScoresController = require('../services/updateSprintScores/updateSprintScoresController');
const sendPliNotificationEmail = require('../services/notificationMentee/sendPLIRatingNotification');
const sendFrozenPliNotification = require('../services/notificationMentee/sendFrozenPliNotification');
const sendOverrideNotificationToMentee = require('../services/notificationMentee/sendPliOverrideNotification');
const sendNotificationMentor = require('../services/notificationMentee/sendNotificationMentor');
const GetPliRatingByIdController = require('../services/getPliRatingById/getPliRatingByIdController');
const GetPliRatingByIdValidator = require('../services/getPliRatingById/getPliRatingByIdValidator');
const GetPliRatingByEmployeeIdController = require('../services/getPliRatingByEmployeeId/getPliRatingByEmployeeIdController');
const GetPliRatingByEmployeeIdValidator = require('../services/getPliRatingByEmployeeId/getPliRatingByEmployeeIdValidator');
const AuthMiddleWare = require('../middleware/auth');

// Get mentee projects
router.get(
    '/pli-projects',
    getMenteeProjectsValidator,
    pliController.getMenteeProjects
);

// Get mentee projects by label
router.get(
    '/pli-projects-by-label',
    getMenteeProjectsByLabelValidator,
    pliController.getMenteeProjectsByLabel
);

// Sync projects
router.post(
    '/pli-projects/sync',
    syncProjectsValidator,
    pliController.syncProjectWeights
);

// These routes were using the deleted PLIRatingController from getPLIScores
// They are commented out as the module has been deleted
// router.get('/pli-rating/auto-fill', PLIRatingController.autoFillFromRAG);
// router.get('/pli-rating', PLIRatingController.getPLIRating);

// Get PLI rating by ID
router.get('/pli-rating/by-id', AuthMiddleWare, (req, res, next) => {
    const validator = new GetPliRatingByIdValidator(req.query, res.__);
    validator.validateGetPliRatingById();
    next();
}, GetPliRatingByIdController.getPliRatingById);

// Update sprint scores in PLI rating
router.post('/pli-rating/sprint-scores', AuthMiddleWare, UpdateSprintScoresController.updateSprintScores);

// Get PLI ratings by employee ID
router.get('/pli-rating/by-employee-id', AuthMiddleWare, (req, res, next) => {
    GetPliRatingByEmployeeIdValidator.validateGetPliRatingByEmployeeId(req.query.employeeId, 'Employee ID');
    next();
}, GetPliRatingByEmployeeIdController.getPliRatingByEmployeeId);

module.exports = router;

// for testing

router.post('/send-pli-email', async (req, res) => {
    const { userId, payload } = req.body;

    if (!userId || !payload) {
        return res.status(400).json({ message: 'userId and payload are required' });
    }

    const success = await sendPliNotificationEmail(userId, payload);

    if (success) {
        return res.status(200).json({ message: 'Email sent successfully' });
    } else {
        return res.status(500).json({ message: 'Failed to send email' });
    }
});
router.post('/queryNotificationEmail', async (req, res) => {
    try {
        const { menteeId, mentorId, ...payload } = req.body;

        if (!menteeId || !mentorId) {
            return res.status(400).json({
                success: false,
                message: 'menteeId and mentorId are required.'
            });
        }

        const result = await sendNotificationMentor(mentorId, {
            menteeId,
            ...payload
        });

        if (result) {
            return res
                .status(200)
                .json({ success: true, message: 'Notification sent successfully.' });
        }

        return res.status(500).json({
            success: false,
            message: 'Failed to send the notification.'
        });
    } catch (error) {
        console.error('Notification error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error: ' + error.message
        });
    }
});

router.post('/frozenPliNotificationEmail', async (req, res) => {
    try {
        const { adminId, ...payload } = req.body;
        if (!adminId) {
            throw new Error('adminId is required');
        }
        const result = await sendFrozenPliNotification(adminId, payload);
        if (result) {
            return res
                .status(200)
                .json({ success: true, message: 'Notification sent.' });
        }
        return res
            .status(500)
            .json({ success: false, message: 'Failed to send notification.' });
    } catch (err) {
        return res.status(500).json({ success: false, message: err.message });
    }
});

router.post('/pli/override-notification-mentee', async (req, res) => {
    try {
        const { menteeId, mentorId, ...payload } = req.body;
        if (!menteeId || !mentorId) {
            return res.status(400).json({
                success: false,
                message: 'menteeId and mentorId are required'
            });
        }
        const result = await sendOverrideNotificationToMentee(
            menteeId,
            mentorId,
            payload
        );
        if (result) {
            return res
                .status(200)
                .json({ success: true, message: 'Notification sent.' });
        }
        return res
            .status(500)
            .json({ success: false, message: 'Failed to send notification.' });
    } catch (err) {
        return res.status(500).json({ success: false, message: err.message });
    }
});
