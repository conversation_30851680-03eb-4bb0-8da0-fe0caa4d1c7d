/**
 * Tests for CourseAssignment User Interactions
 * Testing user interactions, form submissions, and modal behaviors
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';

import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock data
const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-id',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/course',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'pending',
      },
    ],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockMentorUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // Mock getUserData to return the specified user
  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment User Interactions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Completion Percentage Updates', () => {
    it('should update completion percentage for mentors', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and update completion percentage
      const percentageInput = screen.getByDisplayValue('75');
      fireEvent.change(percentageInput, { target: { value: '85' } });
      fireEvent.blur(percentageInput);

      // Should call the API to update percentage
      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/status'),
          expect.objectContaining({
            method: 'PATCH',
            body: expect.stringContaining('85'),
          }),
        );
      });
    });

    it('should handle completion percentage update on Enter key', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and update completion percentage with Enter key
      const percentageInput = screen.getByDisplayValue('75');
      fireEvent.change(percentageInput, { target: { value: '90' } });
      fireEvent.keyDown(percentageInput, { key: 'Enter', code: 'Enter' });

      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/status'),
          expect.objectContaining({
            method: 'PATCH',
            body: expect.stringContaining('90'),
          }),
        );
      });
    });

    it('should handle completion percentage update failure', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.reject(new Error('Update failed'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      const percentageInput = screen.getByDisplayValue('75');
      fireEvent.change(percentageInput, { target: { value: '85' } });
      fireEvent.blur(percentageInput);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/status'),
          expect.any(Object),
        );
      });
    });

    it('should validate percentage bounds (0-100)', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Test upper bound
      const percentageInput = screen.getByDisplayValue('75');
      fireEvent.change(percentageInput, { target: { value: '150' } });
      fireEvent.blur(percentageInput);

      // Should clamp to 100
      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/status'),
          expect.objectContaining({
            body: expect.stringContaining('100'),
          }),
        );
      });
    });
  });

  describe('Comments Modal Interactions', () => {
    it('should open and close comments modal for mentors', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click mentor comments button
      const editCommentsButtons = screen.getAllByText('Edit comments');
      fireEvent.click(editCommentsButtons[0]);

      // Should open comments modal
      await waitFor(() => {
        expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
      });

      // Close modal
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      // Modal should close
      await waitFor(() => {
        expect(screen.queryByText('Mentor Comments:')).not.toBeInTheDocument();
      });
    });

    it('should save mentor comments', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open comments modal
      const editCommentsButtons = screen.getAllByText('Edit comments');
      fireEvent.click(editCommentsButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
      });

      // Edit comments
      const textarea = screen.getByDisplayValue('Keep up the good work');
      fireEvent.change(textarea, {
        target: { value: 'Updated mentor comments' },
      });

      // Save comments
      const saveButton = screen.getByText('Save Comments');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/update-comments'),
          expect.objectContaining({
            method: 'POST',
            body: expect.stringContaining('Updated mentor comments'),
          }),
        );
      });
    });

    it('should handle comments save failure', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.reject(new Error('Save failed'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open and try to save comments
      const editCommentsButtons = screen.getAllByText('Edit comments');
      fireEvent.click(editCommentsButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
      });

      const saveButton = screen.getByText('Save Comments');
      fireEvent.click(saveButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/update-comments'),
          expect.any(Object),
        );
      });
    });
  });

  describe('Document Preview and Approval', () => {
    it('should open document preview modal', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click document button
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        // Should open document preview modal
        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
          expect(screen.getByText('Reject Document')).toBeInTheDocument();
        });
      }
    });

    it('should approve document', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/document-approval')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open document preview and approve
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
        });

        const approveButton = screen.getByText('Approve Document');
        fireEvent.click(approveButton);

        await waitFor(() => {
          expect(mockRequest).toHaveBeenCalledWith(
            expect.stringContaining('/tech-roadmap/document-approval'),
            expect.objectContaining({
              method: 'POST',
              body: expect.stringContaining('approved'),
            }),
          );
        });
      }
    });
  });
});
