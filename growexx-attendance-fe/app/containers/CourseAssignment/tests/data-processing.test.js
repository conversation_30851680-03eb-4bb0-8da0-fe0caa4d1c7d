/**
 * Tests for CourseAssignment Data Processing and State Management
 * Testing data transformation, filtering, and state updates
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock data
const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockAssignmentsWithVariousStates = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-1',
    mentorId: 'mentor-id',
    completionPercentage: undefined, // Test undefined percentage
    completionStatus: 'Completed',
    learningMedium: 'https://example.com/course',
    documents: [],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
  {
    _id: 'assignment-2',
    courseName: 'Node.js Fundamentals',
    duration: '2 Months',
    dueDate: '2024-04-01',
    menteeName: 'Alice Johnson',
    mentorName: 'Bob Wilson',
    employeeId: 'EMP002',
    menteeId: 'mentee-2',
    mentorId: 'mentor-id',
    completionPercentage: null, // Test null percentage
    completionStatus: 'Assigned',
    learningMedium: '',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'approved',
      },
    ],
    menteeComments: '',
    mentorComments: '',
  },
  {
    _id: 'assignment-3',
    courseName: 'Python Basics',
    duration: '1 Month',
    dueDate: '2024-05-01',
    menteeName: 'Charlie Brown',
    mentorName: 'Diana Prince',
    employeeId: 'EMP003',
    menteeId: 'mentee-3',
    mentorId: 'other-mentor',
    completionPercentage: 50,
    completionStatus: 'Rated',
    learningMedium: 'https://example.com/python',
    uploadedDocument: 'https://example.com/legacy-doc.pdf', // Legacy format
    approvalStatus: 'rejected',
    menteeComments: 'Need help with advanced topics',
    mentorComments: 'Will schedule additional sessions',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // Mock getUserData to return the specified user
  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Data Processing', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Completion Percentage Calculation', () => {
    it('should calculate completion percentage based on status when not provided', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignmentsWithVariousStates });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show calculated percentages
      await waitFor(() => {
        // Completed status should show 100%
        expect(screen.getByText('100%')).toBeInTheDocument();
        // Assigned status should show 78%
        expect(screen.getByText('78%')).toBeInTheDocument();
        // Rated status should show 100%
        expect(screen.getByText('50%')).toBeInTheDocument(); // This one has explicit percentage
      });
    });

    it('should handle assignments with missing names gracefully', async () => {
      const Wrapper = createWrapper();

      const assignmentsWithMissingData = [
        {
          _id: 'assignment-1',
          courseName: 'React Advanced',
          // Missing menteeName and mentorName
          employeeId: 'EMP001',
          completionPercentage: 75,
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: assignmentsWithMissingData });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show fallback values
      await waitFor(() => {
        expect(screen.getByText('Unknown Mentee')).toBeInTheDocument();
        expect(screen.getByText('Unknown Mentor')).toBeInTheDocument();
      });
    });
  });

  describe('Document Rendering', () => {
    it('should render both new format and legacy format documents', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignmentsWithVariousStates });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should render document icons with different approval statuses
      const documentButtons = screen.getAllByRole('button');
      const fileButtons = documentButtons.filter(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      expect(fileButtons.length).toBeGreaterThan(0);
    });

    it('should handle assignments with no documents', async () => {
      const Wrapper = createWrapper();

      const assignmentWithNoDocuments = [
        {
          _id: 'assignment-1',
          courseName: 'React Advanced',
          menteeName: 'John Doe',
          mentorName: 'Jane Smith',
          employeeId: 'EMP001',
          completionPercentage: 75,
          documents: [], // Empty documents array
          uploadedDocument: '', // Empty legacy document
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: assignmentWithNoDocuments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show N/A for documents
      await waitFor(() => {
        expect(screen.getByText('N/A')).toBeInTheDocument();
      });
    });
  });

  describe('Search and Filtering', () => {
    it('should filter assignments based on search text', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignmentsWithVariousStates });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Python Basics')).toBeInTheDocument();
      });

      // Search for specific course
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'React' } });

      // Should only show React course
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
        expect(screen.queryByText('Python Basics')).not.toBeInTheDocument();
      });
    });

    it('should filter assignments by mentor for non-admin users', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMentorUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignmentsWithVariousStates });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should only show assignments where mentor-id is the mentor
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
        // Python Basics should not be shown as it has different mentor
        expect(screen.queryByText('Python Basics')).not.toBeInTheDocument();
      });
    });
  });

  describe('URL Parameter Handling', () => {
    it('should extract and use menteeId from URL', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-1']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignmentsWithVariousStates });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should only show assignments for mentee-1
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
        expect(screen.queryByText('Python Basics')).not.toBeInTheDocument();
      });
    });

    it('should handle employeeId parameter and fetch employee profile', async () => {
      const Wrapper = createWrapper(['/course-assignment?employeeId=emp-123']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        if (url.includes('/employee/profile/emp-123')) {
          return Promise.resolve({
            data: { name: 'John Doe', designation: 'Developer' },
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });
  });
});
