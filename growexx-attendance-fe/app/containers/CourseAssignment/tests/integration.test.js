/**
 * Integration Tests for CourseAssignment component
 * Testing complete user workflows and interactions
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock TechRoadmap component
jest.mock(
  '../../TechRoadmap',
  () =>
    function MockTechRoadmap({ visible, onCancel, onSuccess }) {
      if (!visible) return null;
      return (
        <div data-testid="tech-roadmap-modal">
          <h2>Add a new course</h2>
          <button type="button" onClick={onCancel}>
            Cancel
          </button>
          <button type="button" onClick={onSuccess}>
            Submit
          </button>
        </div>
      );
    },
);

// Mock MenteeRoadmapForm component
jest.mock(
  '../../MenteeTechRoadmap/MenteeRoadmapForm',
  () =>
    function MockMenteeRoadmapForm({ onSuccess, onCancel }) {
      return (
        <div data-testid="mentee-roadmap-form">
          <h2>Assign a New Course</h2>
          <button type="button" onClick={onCancel}>
            Cancel
          </button>
          <button type="button" onClick={onSuccess}>
            Submit
          </button>
        </div>
      );
    },
);

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockMenteeUser = {
  _id: 'mentee-id',
  name: 'Mentee User',
  role: 'Developer',
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-id',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/react-course',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'pending',
      },
    ],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Admin Workflow', () => {
    it('should allow admin to view all assignments, search, and manage courses', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/assignment') &&
          options?.method === 'DELETE'
        ) {
          return Promise.resolve({ status: 200 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // 1. Verify admin view loads correctly
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // 2. Test search functionality
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'React' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // 3. Clear search
      fireEvent.change(searchInput, { target: { value: '' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // 4. Test assignment management (assign new course)
      fireEvent.click(screen.getByText('Assign a new course'));

      await waitFor(() => {
        expect(screen.getByTestId('mentee-roadmap-form')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Submit'));

      await waitFor(() => {
        expect(
          screen.queryByTestId('mentee-roadmap-form'),
        ).not.toBeInTheDocument();
      });

      // 5. Test delete functionality
      const deleteButtons = screen.getAllByRole('button');
      const deleteButton = deleteButtons.find(btn =>
        btn.querySelector('[data-icon="delete"]'),
      );

      if (deleteButton) {
        fireEvent.click(deleteButton);

        await waitFor(() => {
          const confirmButton = screen.getByText('Yes');
          fireEvent.click(confirmButton);
        });
      }
    });
  });

  describe('Complete Mentor Workflow', () => {
    it('should allow mentor to manage mentee assignments and approve documents', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 1 });
        }
        if (url.includes('/tech-roadmap/document-approval')) {
          return Promise.resolve({ status: 1 });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // 1. Verify mentor view loads correctly
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // 2. Test completion percentage update
      const percentageInputs = screen.getAllByDisplayValue('75');
      if (percentageInputs.length > 0) {
        const input = percentageInputs[0];
        fireEvent.change(input, { target: { value: '85' } });
        fireEvent.blur(input);
      }

      // 3. Test document approval
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Approve Document'));
      }

      // 4. Test mentor comments
      const editCommentsButtons = screen.getAllByText('Edit comments');
      if (editCommentsButtons.length > 0) {
        fireEvent.click(editCommentsButtons[0]);

        await waitFor(() => {
          expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
        });

        const textareas = screen.getAllByRole('textbox');
        if (textareas.length > 0) {
          fireEvent.change(textareas[0], {
            target: { value: 'Updated mentor comment' },
          });
          fireEvent.click(screen.getByText('Save Comments'));
        }
      }
    });
  });

  describe('Complete Mentee Workflow', () => {
    it('should allow mentee to view assignments, upload documents, and add comments', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      // Mock XMLHttpRequest for file upload
      const mockXHR = {
        open: jest.fn(),
        send: jest.fn(),
        setRequestHeader: jest.fn(),
        onload: null,
        onerror: null,
        status: 200,
        responseText: JSON.stringify({ status: 200 }),
      };

      global.XMLHttpRequest = jest.fn(() => mockXHR);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          const menteeAssignments = mockAssignments.filter(
            assignment => assignment.menteeId === mockMenteeUser._id,
          );
          return Promise.resolve({ data: menteeAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // 1. Verify mentee view loads correctly
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // 2. Verify mentee-specific UI elements
      expect(screen.queryByText('Assign a new course')).not.toBeInTheDocument();
      expect(screen.queryByText('Employee Name')).not.toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Upload Document')).toBeInTheDocument();
      });

      // 3. Test document upload
      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        fireEvent.click(uploadButtons[0]);

        const file = new File(['test content'], 'test.pdf', {
          type: 'application/pdf',
        });

        const createElementSpy = jest.spyOn(document, 'createElement');
        const mockFileInput = {
          type: 'file',
          accept: '.pdf,.jpg,.jpeg,.png',
          style: { display: 'none' },
          onchange: null,
          click: jest.fn(),
          files: [file],
        };

        createElementSpy.mockReturnValue(mockFileInput);

        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [file] } });
        }

        setTimeout(() => {
          if (mockXHR.onload) {
            mockXHR.onload();
          }
        }, 100);
      }

      // 4. Test mentee comments
      const editCommentsButtons = screen.getAllByText('Edit comments');
      if (editCommentsButtons.length > 0) {
        fireEvent.click(editCommentsButtons[0]);

        await waitFor(() => {
          expect(screen.getByText('Mentee Comments:')).toBeInTheDocument();
        });

        const textareas = screen.getAllByRole('textbox');
        if (textareas.length > 0) {
          fireEvent.change(textareas[0], {
            target: { value: 'Updated mentee comment' },
          });
          fireEvent.click(screen.getByText('Save Comments'));
        }
      }
    });
  });

  describe('Cross-Role Interactions', () => {
    it('should handle role transitions and URL parameter changes', async () => {
      // Start as admin
      const history = createMemoryHistory({
        initialEntries: ['/course-assignment'],
      });
      const store = configureStore({}, history);

      // eslint-disable-next-line global-require
      const { getUserData } = require('../../../utils/Helper');
      getUserData.mockReturnValue(mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      const Wrapper = ({ children }) => (
        <Provider store={store}>
          <IntlProvider locale="en">
            <ConnectedRouter history={history}>{children}</ConnectedRouter>
          </IntlProvider>
        </Provider>
      );

      const { rerender } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Verify admin view
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Simulate navigation to mentor view
      history.push('/course-assignment?menteeId=mentee-id');
      getUserData.mockReturnValue(mockMentorUser);

      rerender(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should now show mentor view
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });
    });
  });
});
