/**
 * Component Functionality Tests for CourseAssignment
 * Testing core component functionality and user interactions
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, waitFor, screen, fireEvent } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import CourseAssignment from '../index';
import configureStore from '../../../configureStore';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Fundamentals',
    menteeId: 'admin-id',
    employeeName: '<PERSON> <PERSON>e',
    employeeId: 'EMP001',
    mentorName: 'Jane Smith',
    courseDuration: '30 days',
    targetMonth: 'January 2024',
    learningMedium: 'Online',
    menteeComments: 'Looking forward to learning',
    mentorComments: 'Good progress so far',
    completionPercentage: 75,
    documentStatus: 'approved',
    documents: [
      {
        _id: 'doc-1',
        fileName: 'certificate.pdf',
        uploadDate: '2024-01-15',
        status: 'approved',
      },
    ],
  },
  {
    _id: 'assignment-2',
    courseName: 'Node.js Advanced',
    menteeId: 'user-2',
    employeeName: 'Alice Johnson',
    employeeId: 'EMP002',
    mentorName: 'Bob Wilson',
    courseDuration: '45 days',
    targetMonth: 'February 2024',
    learningMedium: 'Hybrid',
    menteeComments: 'Challenging but interesting',
    mentorComments: 'Needs more practice',
    completionPercentage: 50,
    documentStatus: 'pending',
    documents: [],
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Component Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockRequest.mockClear();
  });

  describe('Component Rendering', () => {
    it('should render with admin user and show admin title', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Check if assignments are rendered
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Node.js Advanced')).toBeInTheDocument();
      });
    });

    it('should render search input', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByPlaceholderText(
            'Search by employee name, course name, or mentor',
          ),
        ).toBeInTheDocument();
      });
    });

    it('should render table with correct columns for admin', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('Employee Name')).toBeInTheDocument();
        expect(screen.getByText('Employee ID')).toBeInTheDocument();
        expect(screen.getByText('Mentors')).toBeInTheDocument();
        expect(screen.getByText('Course name')).toBeInTheDocument();
        expect(screen.getByText('Course duration')).toBeInTheDocument();
        expect(screen.getByText('Target Month')).toBeInTheDocument();
        expect(screen.getByText('Documents')).toBeInTheDocument();
        expect(screen.getByText('Learning Medium')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    it('should filter assignments by course name', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for assignments to load
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Node.js Advanced')).toBeInTheDocument();
      });

      // Search for React
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'React' } });

      // Should show only React course
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.queryByText('Node.js Advanced')).not.toBeInTheDocument();
      });
    });

    it('should update search input value', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for assignments to load
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Node.js Advanced')).toBeInTheDocument();
      });

      // Test search input functionality
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );

      fireEvent.change(searchInput, { target: { value: 'John' } });
      expect(searchInput.value).toBe('John');

      fireEvent.change(searchInput, { target: { value: 'React' } });
      expect(searchInput.value).toBe('React');
    });

    it('should clear search and show all assignments', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for assignments to load
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Node.js Advanced')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );

      // Search for something
      fireEvent.change(searchInput, { target: { value: 'React' } });
      await waitFor(() => {
        expect(screen.queryByText('Node.js Advanced')).not.toBeInTheDocument();
      });

      // Clear search
      fireEvent.change(searchInput, { target: { value: '' } });
      await waitFor(() => {
        expect(screen.getByText('React Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Node.js Advanced')).toBeInTheDocument();
      });
    });
  });

  describe('User Role Handling', () => {
    it('should render for non-admin user without admin title', async () => {
      const regularUser = {
        _id: 'user-id',
        name: 'Regular User',
        role: 'Developer',
        email: '<EMAIL>',
      };

      const Wrapper = createWrapper(['/course-assignment'], regularUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: regularUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
        expect(
          screen.queryByText('Tech Roadmap & Certifications - Admin'),
        ).not.toBeInTheDocument();
      });
    });
  });
});
