/**
 * Snapshot Tests for CourseAssignment component
 * Testing component rendering consistency across different states
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock TechRoadmap component
jest.mock(
  '../../TechRoadmap',
  () =>
    function MockTechRoadmap() {
      return <div data-testid="tech-roadmap-modal">TechRoadmap Modal</div>;
    },
);

// Mock MenteeRoadmapForm component
jest.mock(
  '../../MenteeTechRoadmap/MenteeRoadmapForm',
  () =>
    function MockMenteeRoadmapForm() {
      return (
        <div data-testid="mentee-roadmap-form">MenteeRoadmapForm Modal</div>
      );
    },
);

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockMenteeUser = {
  _id: 'mentee-id',
  name: 'Mentee User',
  role: 'Developer',
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-id',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/react-course',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'pending',
      },
    ],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
  {
    _id: 'assignment-2',
    courseName: 'Node.js Fundamentals',
    duration: '2 Months',
    dueDate: '2024-04-01',
    menteeName: 'Alice Johnson',
    mentorName: 'Bob Wilson',
    employeeId: 'EMP002',
    menteeId: 'mentee-2',
    mentorId: 'mentor-2',
    completionPercentage: 50,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/nodejs-course',
    documents: [],
    menteeComments: '',
    mentorComments: '',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Snapshot Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Admin View Snapshots', () => {
    it('should match snapshot for admin view with assignments', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });

    it('should match snapshot for admin view with no assignments', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });
  });

  describe('Mentor View Snapshots', () => {
    it('should match snapshot for mentor view with menteeId in URL', () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });

    it('should match snapshot for mentor view without menteeId', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMentorUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });
  });

  describe('Mentee View Snapshots', () => {
    it('should match snapshot for mentee view with own assignments', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          const menteeAssignments = mockAssignments.filter(
            assignment => assignment.menteeId === mockMenteeUser._id,
          );
          return Promise.resolve({ data: menteeAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });

    it('should match snapshot for mentee view with no assignments', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });
  });

  describe('Loading State Snapshots', () => {
    it('should match snapshot for loading state', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      // Mock request to never resolve to simulate loading state
      mockRequest.mockImplementation(() => new Promise(() => {}));

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });
  });

  describe('Error State Snapshots', () => {
    it('should match snapshot for error state', () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.reject(new Error('Failed to fetch user details'));
        }
        return Promise.reject(new Error('API Error'));
      });

      const {
        container: { firstChild },
      } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      expect(firstChild).toMatchSnapshot();
    });
  });
});
