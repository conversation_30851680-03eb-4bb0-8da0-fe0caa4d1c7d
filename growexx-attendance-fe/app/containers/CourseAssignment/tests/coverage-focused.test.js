/**
 * Tests for CourseAssignment Coverage-Focused Scenarios
 * Targeting specific uncovered lines and edge cases to increase coverage
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock data
const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockMenteeUser = {
  _id: 'mentee-id',
  name: 'Mentee User',
  role: 'Developer',
  email: '<EMAIL>',
};

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // Mock getUserData to return the specified user
  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Coverage-Focused Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Employee Profile Fetching', () => {
    it('should handle employee profile fetch success', async () => {
      const Wrapper = createWrapper(['/course-assignment?employeeId=emp-123']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        if (url.includes('/employee/profile/emp-123')) {
          return Promise.resolve({
            data: { name: 'John Doe', designation: 'Developer' },
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });

    it('should handle employee profile fetch with no data', async () => {
      const Wrapper = createWrapper(['/course-assignment?employeeId=emp-123']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        if (url.includes('/employee/profile/emp-123')) {
          return Promise.resolve({ data: null });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });

    it('should handle employee profile fetch failure', async () => {
      const Wrapper = createWrapper(['/course-assignment?employeeId=emp-123']);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        if (url.includes('/employee/profile/emp-123')) {
          return Promise.reject(new Error('Employee not found'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });
  });

  describe('Assignment Processing Edge Cases', () => {
    it('should handle assignments with no data response', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({}); // No data property
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });

    it('should handle assignments with empty data array and show info message', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show info message for no assignments
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });
  });

  describe('Modal Interactions', () => {
    it('should handle course modal success callback', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Open modal
      const assignButton = screen.getByText('Assign a new course');
      fireEvent.click(assignButton);

      await waitFor(() => {
        expect(screen.getByText('Assign a New Course')).toBeInTheDocument();
      });

      // Simulate success callback by clicking submit
      const submitButton = screen.getByText('Submit');
      fireEvent.click(submitButton);
    });

    it('should handle mentee roadmap form modal cancel', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Open modal
      const assignButton = screen.getByText('Assign a new course');
      fireEvent.click(assignButton);

      await waitFor(() => {
        expect(screen.getByText('Assign a New Course')).toBeInTheDocument();
      });

      // Cancel modal
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(
          screen.queryByText('Assign a New Course'),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Document Approval Edge Cases', () => {
    it('should handle document approval with missing IDs', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      const mockAssignments = [
        {
          _id: 'assignment-1',
          courseName: 'React Advanced',
          menteeId: 'mentee-id',
          documents: [
            {
              _id: 'doc-1',
              documentName: 'Certificate.pdf',
              documentLink: 'https://example.com/doc1.pdf',
              approvalStatus: 'pending',
            },
          ],
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/document-approval')) {
          return Promise.resolve({ status: 0 }); // Failure status
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open document preview
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
        });

        // Try to approve
        const approveButton = screen.getByText('Approve Document');
        fireEvent.click(approveButton);
      }
    });
  });

  describe('File Upload Edge Cases', () => {
    it('should handle file upload with successful response but no status', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      const mockAssignments = [
        {
          _id: 'assignment-1',
          courseName: 'React Advanced',
          menteeId: 'mentee-id',
          completionPercentage: 75,
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      // Mock XMLHttpRequest for file upload
      const mockXHR = {
        open: jest.fn(),
        send: jest.fn(),
        setRequestHeader: jest.fn(),
        onload: null,
        onerror: null,
        status: 200,
        responseText: JSON.stringify({ message: 'Upload successful' }), // No status field
      };

      global.XMLHttpRequest = jest.fn(() => mockXHR);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Mock file input and upload
      const createElementSpy = jest.spyOn(document, 'createElement');
      const mockFileInput = {
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        style: { display: 'none' },
        onchange: null,
        click: jest.fn(),
      };

      createElementSpy.mockReturnValue(mockFileInput);

      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        fireEvent.click(uploadButtons[0]);

        const validFile = new File(['test content'], 'test.pdf', {
          type: 'application/pdf',
        });

        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [validFile] } });

          // Simulate successful upload
          setTimeout(() => {
            if (mockXHR.onload) {
              mockXHR.onload();
            }
          }, 100);
        }
      }

      createElementSpy.mockRestore();
    });
  });

  describe('Completion Status Logic', () => {
    it('should handle completion status update with 100% setting status to Completed', async () => {
      const Wrapper = createWrapper(['/course-assignment?menteeId=mentee-id']);

      const mockAssignments = [
        {
          _id: 'assignment-1',
          courseName: 'React Advanced',
          menteeId: 'mentee-id',
          completionPercentage: 90,
          completionStatus: 'Assigned',
        },
      ];

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 200 }); // Different status format
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Update to 100%
      const percentageInput = screen.getByDisplayValue('90');
      fireEvent.change(percentageInput, { target: { value: '100' } });
      fireEvent.blur(percentageInput);

      await waitFor(() => {
        expect(mockRequest).toHaveBeenCalledWith(
          expect.stringContaining('/tech-roadmap/status'),
          expect.objectContaining({
            body: expect.stringContaining('Completed'),
          }),
        );
      });
    });
  });
});
