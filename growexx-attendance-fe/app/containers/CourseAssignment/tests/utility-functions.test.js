/**
 * Tests for CourseAssignment Utility Functions and State Management
 * Testing helper functions, state updates, and internal logic
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock the isSuperAdmin utility
jest.mock('../../../components/SideBar', () => ({
  isSuperAdmin: jest.fn(),
}));

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock data
const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockSuperAdminUser = {
  _id: 'super-admin-id',
  name: 'Super Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-id',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/course',
    documents: [],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // Mock getUserData to return the specified user
  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Utility Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Super Admin Detection', () => {
    it('should show "Add a new course" button for super admin', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockSuperAdminUser);

      // Mock isSuperAdmin to return true
      // eslint-disable-next-line global-require
      const { isSuperAdmin } = require('../../../components/SideBar');
      isSuperAdmin.mockReturnValue(true);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockSuperAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show "Add a new course" button for super admin
      expect(screen.getByText('Add a new course')).toBeInTheDocument();
    });

    it('should show "Assign a new course" button for regular admin', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      // Mock isSuperAdmin to return false
      // eslint-disable-next-line global-require
      const { isSuperAdmin } = require('../../../components/SideBar');
      isSuperAdmin.mockReturnValue(false);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show "Assign a new course" button for regular admin
      expect(screen.getByText('Assign a new course')).toBeInTheDocument();
    });
  });

  describe('Column Generation Logic', () => {
    it('should generate correct columns for admin users', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show admin-specific columns
      expect(screen.getByText('Employee Name')).toBeInTheDocument();
      expect(screen.getByText('Employee ID')).toBeInTheDocument();
      expect(screen.getByText('Mentors')).toBeInTheDocument();
      expect(screen.getByText('Action')).toBeInTheDocument();
    });

    it('should generate correct columns for mentor users with menteeId', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show action column for mentors
      expect(screen.getByText('Action')).toBeInTheDocument();
      // Should show editable completion percentage
      expect(screen.getByDisplayValue('75')).toBeInTheDocument();
    });

    it('should generate correct columns for mentee users', async () => {
      const mockMenteeUser = {
        _id: 'mentee-id',
        name: 'Mentee User',
        role: 'Developer',
        email: '<EMAIL>',
      };

      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show upload document column
      expect(screen.getByText('Upload Document')).toBeInTheDocument();
      // Should NOT show employee name/ID columns
      expect(screen.queryByText('Employee Name')).not.toBeInTheDocument();
      expect(screen.queryByText('Employee ID')).not.toBeInTheDocument();
    });
  });

  describe('State Management', () => {
    it('should handle loading states correctly', async () => {
      const Wrapper = createWrapper();

      let resolveUserDetails;
      let resolveAssignments;

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return new Promise(resolve => {
            resolveUserDetails = resolve;
          });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return new Promise(resolve => {
            resolveAssignments = resolve;
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Initially should show basic structure
      expect(
        screen.getByText('Tech Roadmap & Certifications'),
      ).toBeInTheDocument();

      // Resolve user details
      act(() => {
        resolveUserDetails({ data: mockAdminUser });
      });

      // Resolve assignments
      act(() => {
        resolveAssignments({ data: mockAssignments });
      });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });
    });

    it('should handle modal state management', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Open modal
      const assignButton = screen.getByText('Assign a new course');
      fireEvent.click(assignButton);

      // Modal should be open
      await waitFor(() => {
        expect(screen.getByText('Assign a New Course')).toBeInTheDocument();
      });

      // Close modal
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      // Modal should be closed
      await waitFor(() => {
        expect(screen.queryByText('Assign a New Course')).not.toBeInTheDocument();
      });
    });

    it('should handle search state updates', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Update search
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );

      fireEvent.change(searchInput, { target: { value: 'React' } });

      // Should filter results
      expect(screen.getByText('React Advanced')).toBeInTheDocument();

      // Clear search
      fireEvent.change(searchInput, { target: { value: '' } });

      // Should show all results again
      expect(screen.getByText('React Advanced')).toBeInTheDocument();
    });
  });

  describe('Error Recovery', () => {
    it('should recover from API failures gracefully', async () => {
      const Wrapper = createWrapper();

      let failCount = 0;
      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          failCount += 1;
          if (failCount === 1) {
            return Promise.reject(new Error('Network error'));
          }
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show empty state initially due to error
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle component cleanup on unmount', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return new Promise(() => {}); // Never resolves
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      const { unmount } = render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Unmount component while API call is pending
      unmount();

      // Should not cause memory leaks or errors
      expect(true).toBe(true);
    });
  });

  describe('Date and Time Handling', () => {
    it('should format dates correctly', async () => {
      const Wrapper = createWrapper();

      const assignmentWithDate = [
        {
          ...mockAssignments[0],
          dueDate: '2024-12-25T00:00:00.000Z',
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: assignmentWithDate });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should format date as "MMM YYYY"
      expect(screen.getByText('Dec 2024')).toBeInTheDocument();
    });

    it('should handle invalid dates gracefully', async () => {
      const Wrapper = createWrapper();

      const assignmentWithInvalidDate = [
        {
          ...mockAssignments[0],
          dueDate: 'invalid-date',
        },
      ];

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: assignmentWithInvalidDate });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show N/A for invalid dates
      expect(screen.getByText('N/A')).toBeInTheDocument();
    });
  });
});
