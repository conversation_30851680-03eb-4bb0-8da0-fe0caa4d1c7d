/**
 * Error Handling Tests for CourseAssignment component
 * Testing various error scenarios and edge cases
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import CourseAssignment from '../index';
import configureStore from '../../../configureStore';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // eslint-disable-next-line global-require
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockRequest.mockClear();
  });

  describe('API Error Scenarios', () => {
    it('should handle user details fetch failure gracefully', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.reject(new Error('Failed to fetch user details'));
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Component should still render despite API error
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });

      // Should show empty state
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle assignments fetch failure gracefully', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Failed to fetch assignments'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Component should render with admin title
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show empty state when assignments fail to load
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle empty assignments response', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle malformed assignments response', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: null });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should handle null data gracefully
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle network timeout errors', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Network timeout'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should show empty state on network error
      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });
  });

  describe('Component Resilience', () => {
    it('should handle missing user data', async () => {
      const Wrapper = createWrapper();

      // eslint-disable-next-line global-require
      const { getUserData } = require('../../../utils/Helper');
      getUserData.mockReturnValue(null);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: null });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Component should still render but with limited functionality
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });
    });

    it('should handle corrupted assignment data', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({
            data: [
              {
                _id: 'assignment-1',
                // Missing required fields
              },
              null,
              undefined,
              'invalid-data',
            ],
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should render without crashing
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });
    });

    it('should handle API rate limiting errors', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Too Many Requests'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle server maintenance errors', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Service Unavailable'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle empty response data gracefully', async () => {
      const Wrapper = createWrapper();

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({});
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle different user roles', async () => {
      const mentorUser = {
        _id: 'mentor-id',
        name: 'Mentor User',
        role: 'Mentor',
        email: '<EMAIL>',
      };

      const Wrapper = createWrapper(['/course-assignment'], mentorUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });
    });

    it('should handle developer role with assignments', async () => {
      const developerUser = {
        _id: 'dev-id',
        name: 'Developer User',
        role: 'Developer',
        email: '<EMAIL>',
      };

      const Wrapper = createWrapper(['/course-assignment'], developerUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: developerUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({
            data: [
              {
                _id: 'assignment-1',
                courseName: 'React Basics',
                menteeId: 'dev-id',
                employeeName: 'Developer User',
                employeeId: 'DEV001',
                mentorName: 'Senior Dev',
                courseDuration: '30 days',
                targetMonth: 'January 2024',
                learningMedium: 'Online',
                menteeComments: '',
                mentorComments: '',
                completionPercentage: 0,
                documentStatus: 'pending',
              },
            ],
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Basics')).toBeInTheDocument();
      });
    });
  });
});
