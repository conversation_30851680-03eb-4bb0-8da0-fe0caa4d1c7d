/**
 * Tests for CourseAssignment constants
 */

import { API_ENDPOINTS } from '../constants';

describe('CourseAssignment Constants', () => {
  describe('API_ENDPOINTS', () => {
    it('should have all required API endpoints defined', () => {
      expect(API_ENDPOINTS).toBeDefined();
      expect(typeof API_ENDPOINTS).toBe('object');
    });

    it('should have EMPLOYEE_PROFILE endpoint', () => {
      expect(API_ENDPOINTS.EMPLOYEE_PROFILE).toBeDefined();
      expect(typeof API_ENDPOINTS.EMPLOYEE_PROFILE).toBe('string');
      expect(API_ENDPOINTS.EMPLOYEE_PROFILE).toContain('/pli/employee-profile');
    });

    it('should have USER_DETAILS_API endpoint', () => {
      expect(API_ENDPOINTS.USER_DETAILS_API).toBeDefined();
      expect(typeof API_ENDPOINTS.USER_DETAILS_API).toBe('string');
      expect(API_ENDPOINTS.USER_DETAILS_API).toContain('/user/details');
    });

    it('should have TECH_ROADMAP_ALL_ASSIGNMENTS endpoint', () => {
      expect(API_ENDPOINTS.TECH_ROADMAP_ALL_ASSIGNMENTS).toBeDefined();
      expect(typeof API_ENDPOINTS.TECH_ROADMAP_ALL_ASSIGNMENTS).toBe('string');
      expect(API_ENDPOINTS.TECH_ROADMAP_ALL_ASSIGNMENTS).toContain(
        '/tech-roadmap/all-assignments',
      );
    });

    it('should have UPLOAD_COURSE_DOCUMENT endpoint', () => {
      expect(API_ENDPOINTS.UPLOAD_COURSE_DOCUMENT).toBeDefined();
      expect(typeof API_ENDPOINTS.UPLOAD_COURSE_DOCUMENT).toBe('string');
      expect(API_ENDPOINTS.UPLOAD_COURSE_DOCUMENT).toContain(
        '/tech-roadmap/upload-document',
      );
    });

    it('should have DELETE_COURSE_ASSIGNMENT endpoint', () => {
      expect(API_ENDPOINTS.DELETE_COURSE_ASSIGNMENT).toBeDefined();
      expect(typeof API_ENDPOINTS.DELETE_COURSE_ASSIGNMENT).toBe('string');
      expect(API_ENDPOINTS.DELETE_COURSE_ASSIGNMENT).toContain(
        '/tech-roadmap/assignment',
      );
    });

    it('should have DOCUMENT_APPROVAL endpoint', () => {
      expect(API_ENDPOINTS.DOCUMENT_APPROVAL).toBeDefined();
      expect(typeof API_ENDPOINTS.DOCUMENT_APPROVAL).toBe('string');
      expect(API_ENDPOINTS.DOCUMENT_APPROVAL).toContain(
        '/tech-roadmap/document-approval',
      );
    });

    it('should have UPDATE_COMMENTS endpoint', () => {
      expect(API_ENDPOINTS.UPDATE_COMMENTS).toBeDefined();
      expect(typeof API_ENDPOINTS.UPDATE_COMMENTS).toBe('string');
      expect(API_ENDPOINTS.UPDATE_COMMENTS).toContain(
        '/tech-roadmap/update-comments',
      );
    });

    it('should have UPDATE_ASSIGNMENT endpoint', () => {
      expect(API_ENDPOINTS.UPDATE_ASSIGNMENT).toBeDefined();
      expect(typeof API_ENDPOINTS.UPDATE_ASSIGNMENT).toBe('string');
      expect(API_ENDPOINTS.UPDATE_ASSIGNMENT).toContain(
        '/tech-roadmap/update-assignment',
      );
    });

    it('should have TECH_ROADMAP_STATUS endpoint', () => {
      expect(API_ENDPOINTS.TECH_ROADMAP_STATUS).toBeDefined();
      expect(typeof API_ENDPOINTS.TECH_ROADMAP_STATUS).toBe('string');
      expect(API_ENDPOINTS.TECH_ROADMAP_STATUS).toContain(
        '/tech-roadmap/status',
      );
    });

    it('should have all endpoints as strings', () => {
      Object.values(API_ENDPOINTS).forEach(endpoint => {
        expect(typeof endpoint).toBe('string');
        expect(endpoint.length).toBeGreaterThan(0);
      });
    });

    it('should have all endpoints starting with API_URL', () => {
      Object.values(API_ENDPOINTS).forEach(endpoint => {
        // Since API_URL is imported from constants, we just check that endpoints are properly formatted
        expect(endpoint).toMatch(/^.*\/.*$/); // Should contain at least one slash
      });
    });
  });
});
