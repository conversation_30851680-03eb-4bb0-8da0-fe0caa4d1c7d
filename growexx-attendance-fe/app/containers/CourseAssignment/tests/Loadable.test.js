/**
 * Test for CourseAssignment Loadable component
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import configureStore from '../../../configureStore';
import LoadableCourseAssignment from '../Loadable';

const createWrapper = () => {
  const history = createMemoryHistory();
  const store = configureStore({}, history);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('<CourseAssignment Loadable />', () => {
  it('Should render and match the snapshot', () => {
    const Wrapper = createWrapper();
    const {
      container: { firstChild },
    } = render(
      <Wrapper>
        <LoadableCourseAssignment />
      </Wrapper>,
    );
    expect(firstChild).toMatchSnapshot();
  });
});
