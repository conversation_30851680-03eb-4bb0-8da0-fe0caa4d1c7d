// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CourseAssignment Snapshot Tests Admin View Snapshots should match snapshot for admin view with assignments 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        >
          <button
            class="ant-btn ant-btn-primary"
            style="background-color: rgb(77, 24, 110); border-color: #4d186e;"
            type="button"
          >
            <span>
              Assign a new course
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Admin View Snapshots should match snapshot for admin view with no assignments 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        >
          <button
            class="ant-btn ant-btn-primary"
            style="background-color: rgb(77, 24, 110); border-color: #4d186e;"
            type="button"
          >
            <span>
              Assign a new course
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Error State Snapshots should match snapshot for error state 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        >
          <button
            class="ant-btn ant-btn-primary"
            style="background-color: rgb(77, 24, 110); border-color: #4d186e;"
            type="button"
          >
            <span>
              Assign a new course
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Loading State Snapshots should match snapshot for loading state 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        >
          <button
            class="ant-btn ant-btn-primary"
            style="background-color: rgb(77, 24, 110); border-color: #4d186e;"
            type="button"
          >
            <span>
              Assign a new course
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Mentee View Snapshots should match snapshot for mentee view with no assignments 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        />
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Mentee View Snapshots should match snapshot for mentee view with own assignments 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        />
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Mentor View Snapshots should match snapshot for mentor view with menteeId in URL 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        />
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;

exports[`CourseAssignment Snapshot Tests Mentor View Snapshots should match snapshot for mentor view without menteeId 1`] = `
<div
  style="padding: 24px;"
>
  <div
    class="ant-card ant-card-bordered"
    style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e8e8e8;"
  >
    <div
      class="ant-card-head"
      style="background-color: rgb(246, 246, 246); border-bottom: 1px solid #e8e8e8; padding: 16px 24px; font-size: 18px; font-weight: 500; color: rgb(77, 24, 110);"
    >
      <div
        class="ant-card-head-wrapper"
      >
        <div
          class="ant-card-head-title"
        >
          Tech Roadmap & Certifications
        </div>
        <div
          class="ant-card-extra"
        />
      </div>
    </div>
    <div
      class="ant-card-body"
    >
      <div
        style="margin-bottom: 16px;"
      >
        <span
          class="ant-input-affix-wrapper"
          style="width: 300px;"
        >
          <span
            class="ant-input-prefix"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input"
            placeholder="Search by employee name, course name, or mentor"
            type="text"
            value=""
          />
        </span>
      </div>
      <div
        class="ant-table-wrapper"
      >
        <div
          class="ant-spin-nested-loading"
        >
          <div
            class="ant-spin-container"
          >
            <div
              class="ant-table ant-table-empty"
            >
              <div
                class="ant-table-container"
              >
                <div
                  class="ant-table-content"
                >
                  <table
                    style="table-layout: auto;"
                  >
                    <colgroup />
                    <thead
                      class="ant-table-thead"
                    >
                      <tr>
                        <th
                          class="ant-table-cell"
                        >
                          Employee Name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Employee ID
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentors
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course name
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Course duration
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Target Month
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Documents
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Learning Medium
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentee comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          Mentor comments
                        </th>
                        <th
                          class="ant-table-cell"
                        >
                          % of completion
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="ant-table-tbody"
                    >
                      <tr
                        class="ant-table-placeholder"
                      >
                        <td
                          class="ant-table-cell"
                          colspan="11"
                        >
                          No courses assigned yet
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="tech-roadmap-modal"
  >
    TechRoadmap Modal
  </div>
</div>
`;
